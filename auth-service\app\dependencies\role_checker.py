from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>earer
from jose import J<PERSON><PERSON>rror, jwt
from typing import List
from app.config import SECRET_KEY, ALGORITHM

# Make sure this matches your actual login endpoint (with prefix if using a router)
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/auth/login")

# ✅ Decode JWT and extract roles
def get_current_user_roles(token: str = Depends(oauth2_scheme)) -> List[str]:
    #Dependency that decodes the JWT token and returns the user's roles.
    
    #- Extracts the token from the Authorization header.
    #- Decodes it using the shared SECRET_KEY and ALGORITHM.
    #- Validates that 'roles' exist and are of list type.
    
    #Raises:
        #HTTP 403 if token is invalid or roles are missing/invalid.
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        roles = payload.get("roles")
        if not roles or not isinstance(roles, list):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Roles not found or invalid"
            )
        return roles
    except JW<PERSON>rror as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid token"
        )

# ✅ Require specific role(s)
def require_role(required_roles: List[str]):
    #Factory function that returns a dependency to enforce role-based access.

    #- `required_roles`: list of roles allowed to access the route.

    #Returns:
        # A dependency that checks if the current user has at least one of the required roles.

    #Raises:
        #HTTP 403 if none of the required roles are present in the token's roles.
    def role_checker(roles: List[str] = Depends(get_current_user_roles)):
        if not any(role in required_roles for role in roles):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access forbidden. Required roles: {required_roles}"
            )
        return True
    return role_checker
