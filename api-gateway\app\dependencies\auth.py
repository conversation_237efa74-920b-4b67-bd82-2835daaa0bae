from fastapi import Depends, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
import httpx
from app.core.config import AUTH_SERVICE_URL

security = HTTPBearer()

async def verify_token(token: str = Depends(security)):
    """Verify token with Auth Service"""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
    f"{AUTH_SERVICE_URL}/auth/me",
    headers={"Authorization": f"Bearer {token.credentials}"}
)
            if response.status_code != 200:
                raise HTTPException(status_code=401, detail="Invalid token")
            return response.json()
    except httpx.RequestError:
        raise HTTPException(status_code=503, detail="Auth service unavailable")
