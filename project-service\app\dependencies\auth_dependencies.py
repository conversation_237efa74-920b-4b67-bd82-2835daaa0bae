from fastapi import Depends, HTTPException, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import jwt, JW<PERSON>rror
from sqlalchemy.orm import Session
from app.database.connection import SessionLocal
from app.config import SECRET_KEY, ALGORITHM
from app.controller import user as crud_user

# Add HTTPBearer for Swagger UI support
security = HTTPBearer()

# Get database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Extract bearer token from Authorization header (your original method)
def get_token_from_header(request: Request):
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Invalid authorization header")
    return auth_header.split(" ")[1]

# Alternative method using HTTPBearer for Swagger UI compatibility
def get_token_from_bearer(credentials: HTTPAuthorizationCredentials = Depends(security)):
    return credentials.credentials

# Main dependency to get the current user from token
# Use HTTPBearer method for better Swagger UI support
def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials"
    )
    try:
        # Extract token from HTTPBearer credentials
        token = credentials.credentials
        
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        email = payload.get("sub")
        if email is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception

    user = crud_user.get_user_by_email(db, email)
    if user is None:
        raise credentials_exception

    return user

# Alternative version using your original method (if you prefer)
def get_current_user_custom_header(
    token: str = Depends(get_token_from_header),
    db: Session = Depends(get_db)
):
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials"
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        email = payload.get("sub")
        if email is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception

    user = crud_user.get_user_by_email(db, email)
    if user is None:
        raise credentials_exception

    return user