"""
MongoDB Logging Utility for Auth Service

This module provides enterprise-level logging functionality that:
1. <PERSON><PERSON><PERSON> logs to a local file for performance
2. Periodically syncs logs to MongoDB using a background thread
3. Maintains the specified log format with proper error handling
4. Provides clean, minimal integration with existing code
"""

import json
import os
import threading
import time
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path
import uuid

from pymongo import MongoClient
from pymongo.errors import PyMongoError

from app.config import MON<PERSON><PERSON>B_URL, MONGODB_DATABASE, MONGODB_COLLECTION, MONGODB_SYNC_INTERVAL


class MongoLogger:
    """
    Enterprise-level MongoDB logger with file buffering and background sync.
    
    Features:
    - File-based buffering for high performance
    - Background thread for MongoDB synchronization
    - Automatic log rotation and cleanup
    - Thread-safe operations
    - Graceful error handling
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """Singleton pattern to ensure single logger instance"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(Mongo<PERSON>ogger, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """Initialize the MongoDB logger with file buffering"""
        if hasattr(self, '_initialized'):
            return
            
        self._initialized = True
        self.log_file_path = Path("logs/auth_service.log")
        self.log_file_path.parent.mkdir(exist_ok=True)
        
        # MongoDB connection settings
        self.mongodb_url = MONGODB_URL
        self.mongodb_database = MONGODB_DATABASE
        self.mongodb_collection = MONGODB_COLLECTION
        
        # Threading and synchronization
        self.file_lock = threading.Lock()
        self.sync_interval = MONGODB_SYNC_INTERVAL  # Configurable sync interval from env
        self.running = True
        
        # Start background sync thread
        self.sync_thread = threading.Thread(target=self._background_sync, daemon=True)
        self.sync_thread.start()
    
    def log(self, 
            source: str,
            service: str = "auth-service",
            component: str = "",
            level: str = "INFO",
            message: str = "",
            session_id: str = "",
            metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Log a message with the specified format.
        
        Args:
            source: Source of the log (e.g., "controller", "router")
            service: Service name (default: "auth-service")
            component: Component name (e.g., "login", "register")
            level: Log level (INFO, ERROR, WARNING, DEBUG)
            message: Log message
            session_id: Session identifier
            metadata: Additional metadata dictionary
        """
        try:
            # Create log entry in the specified format
            log_entry = {
                "_id": str(uuid.uuid4().hex[:24]),  # 24-character hex string
                "source": source,
                "timestamp": datetime.utcnow().isoformat() + "Z",
                "date": datetime.utcnow().strftime("%Y-%m-%d"),
                "service": service,
                "component": component,
                "level": level.upper(),
                "message": message,
                "sessionId": session_id,
                "metadata": metadata or {}
            }
            
            # Write to file buffer (thread-safe)
            with self.file_lock:
                with open(self.log_file_path, 'a', encoding='utf-8') as f:
                    f.write(json.dumps(log_entry) + '\n')
                    
        except Exception as e:
            # Fallback logging to prevent application crashes
            print(f"MongoLogger Error: Failed to write log - {str(e)}")
    
    def _background_sync(self) -> None:
        """Background thread that syncs logs from file to MongoDB at configurable intervals"""
        while self.running:
            try:
                time.sleep(self.sync_interval)
                self._sync_logs_to_mongodb()
            except Exception as e:
                print(f"MongoLogger Sync Error: {str(e)}")
    
    def _sync_logs_to_mongodb(self) -> None:
        """Sync buffered logs from file to MongoDB and clear the file"""
        if not self.log_file_path.exists():
            return
            
        try:
            # Read all logs from file (thread-safe)
            logs_to_sync = []
            with self.file_lock:
                if self.log_file_path.stat().st_size > 0:
                    with open(self.log_file_path, 'r', encoding='utf-8') as f:
                        for line in f:
                            line = line.strip()
                            if line:
                                try:
                                    log_entry = json.loads(line)
                                    logs_to_sync.append(log_entry)
                                except json.JSONDecodeError:
                                    continue
                    
                    # Clear the file after reading
                    open(self.log_file_path, 'w').close()
            
            # Insert logs to MongoDB if any exist
            if logs_to_sync:
                self._insert_to_mongodb(logs_to_sync)
                
        except Exception as e:
            print(f"MongoLogger Sync Error: Failed to sync logs - {str(e)}")
    
    def _insert_to_mongodb(self, logs: list) -> None:
        """Insert logs to MongoDB with proper error handling"""
        try:
            client = MongoClient(self.mongodb_url, serverSelectionTimeoutMS=5000)
            db = client[self.mongodb_database]
            collection = db[self.mongodb_collection]
            
            # Insert logs in batch
            if logs:
                collection.insert_many(logs)
                print(f"MongoLogger: Successfully synced {len(logs)} logs to MongoDB")
                
        except PyMongoError as e:
            print(f"MongoLogger MongoDB Error: {str(e)}")
            # Re-write logs back to file if MongoDB insert fails
            self._rewrite_failed_logs(logs)
        except Exception as e:
            print(f"MongoLogger Unexpected Error: {str(e)}")
            self._rewrite_failed_logs(logs)
        finally:
            try:
                client.close()
            except:
                pass
    
    def _rewrite_failed_logs(self, logs: list) -> None:
        """Re-write logs back to file if MongoDB insertion fails"""
        try:
            with self.file_lock:
                with open(self.log_file_path, 'a', encoding='utf-8') as f:
                    for log in logs:
                        f.write(json.dumps(log) + '\n')
        except Exception as e:
            print(f"MongoLogger: Failed to rewrite logs - {str(e)}")
    
    def shutdown(self) -> None:
        """Gracefully shutdown the logger and sync remaining logs"""
        self.running = False
        if self.sync_thread.is_alive():
            self.sync_thread.join(timeout=10)
        
        # Final sync of remaining logs
        self._sync_logs_to_mongodb()


# Global logger instance
logger = MongoLogger()


def log_info(source: str, component: str, message: str, session_id: str = "", metadata: Dict[str, Any] = None) -> None:
    """Convenience function for INFO level logging"""
    logger.log(source=source, component=component, level="INFO", message=message, 
               session_id=session_id, metadata=metadata)


def log_error(source: str, component: str, message: str, session_id: str = "", metadata: Dict[str, Any] = None) -> None:
    """Convenience function for ERROR level logging"""
    logger.log(source=source, component=component, level="ERROR", message=message, 
               session_id=session_id, metadata=metadata)


def log_warning(source: str, component: str, message: str, session_id: str = "", metadata: Dict[str, Any] = None) -> None:
    """Convenience function for WARNING level logging"""
    logger.log(source=source, component=component, level="WARNING", message=message, 
               session_id=session_id, metadata=metadata)
