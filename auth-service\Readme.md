#  Auth-Service Project

This is a FastAPI-based microservice for handling authentication, including user login and token generation, built with Docker support and environment-based configuration.

---

##  Features

- FastAPI with automatic OpenAPI docs (Swagger UI)
- Dockerized for easy deployment
- Supports `.env` configuration for environment variables

POST    /auth/register         → Create user 
POST    /auth/login            → get the token
POST    /auth/google-login     → get google login token 
POST    /auth/refresh          → token refersh 
Get     /auth/me               →  to validate the user
---
##  Local Development Setup



1.Create a Virtual Environment
 "python -m venv venv" 
2. Activate the Environment 
On Windows "venv\Scripts\activate"
On Mac/Linux: "source venv/bin/activate"
3.Install Required Packages
 "pip install -r requirements.txt"
4.Run the Application Locally
"uvicorn app.main:app --port 8001 --reload"
5.Open in Browser
to check the web browser "http://127.0.0.1:8001/docs"

# Docker in local 
1. Build the Docker Image
docker build -t auth-service .
2. Run the Docker Container
docker run -d -p 8001:8001 --env-file .env --name auth-container auth-service
3. Access in Browser
To docker check the web browser
http://localhost:8001

Docker OS 
OSType: linux





