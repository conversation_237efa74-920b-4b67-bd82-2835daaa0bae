from sqlalchemy.orm import Session
from datetime import datetime
from app.models.organization import Organization
from app.schemas.organization import OrganizationCreate,OrganizationUpdate
from datetime import datetime
from fastapi import HTTPException


def get_all(db: Session):
    return db.query(Organization).all()

def get_by_id(db: Session, id: int):
    return db.query(Organization).filter(Organization.id == id).first()

def create(db: Session, org: OrganizationCreate):
    # Check if org with same name already exists
    existing_org = db.query(Organization).filter(Organization.name == org.name).first()
    if existing_org:
        raise HTTPException(status_code=400, detail="Organization already exists")

    # Proceed to create new organization
    new_org = Organization(
        name=org.name,
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    db.add(new_org)
    db.commit()
    db.refresh(new_org)
    return new_org

def update(db: Session, id: int, org: OrganizationUpdate):
    db_org = get_by_id(db, id)
    if db_org:
        db_org.name = org.name
        db_org.updated_at = datetime.now()
        db.commit()
        db.refresh(db_org)
    return db_org

def delete(db: Session, id: int):
    db_org = get_by_id(db, id)
    if db_org:
        db.delete(db_org)
        db.commit()
    return db_org