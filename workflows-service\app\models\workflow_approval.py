from sqlalchemy import Column, BigInteger, ForeignKey, Text, Enum, DateTime,Integer,String
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum

from app.database.connection import Base
from enum import Enum as PyEnum
from sqlalchemy import Enum as SqlEnum


# Define the Python Enum used by SQLAlchemy
class ApprovalStatus(PyEnum):
    approved = "approved"
    rejected = "rejected"


class WorkflowApproval(Base):
    __tablename__ = "workflow_approvals"

    id = Column(Integer, primary_key=True)
    workflow_id = Column(Integer, nullable=False)

    approver_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    status = Column(SqlEnum(ApprovalStatus, name="approval_status"), nullable=False)

    comment = Column(String, nullable=True)
    approved_at = Column(DateTime, default=None)

    # Relationship to User
    approver = relationship("User", back_populates="approvals_given")




