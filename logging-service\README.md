# Centralized MongoDB Logging Service

## Overview

This centralized logging service provides enterprise-level MongoDB logging with file buffering and background synchronization for all microservices. It eliminates code duplication and provides consistent logging across the entire system.

## Features

- **Centralized Management**: Single logging service for all microservices
- **Service-Specific Files**: Each service gets its own log file in a common directory
- **File Buffering**: High-performance logging with local file buffering
- **Background Sync**: Automatic MongoDB synchronization at configurable intervals
- **Thread-Safe**: Safe for concurrent use across multiple services
- **Error Handling**: Graceful handling of MongoDB connection issues
- **Session Tracking**: Complete request tracing with unique session IDs
- **Easy Integration**: Simple dependency integration for all microservices

## Architecture

```
logging-service/
├── __init__.py                    # Package initialization
├── config.py                     # Centralized configuration
├── mongo_logger.py               # Core logging functionality
├── service_logger.py             # Service integration helper
├── test_centralized_logging.py   # Test script
├── requirements.txt              # Dependencies
└── README.md                     # This file

logs/                             # Common logs directory
├── agent-service_service.log     # Agent service logs
├── auth-service_service.log      # Auth service logs
├── organization-service_service.log
├── project-service_service.log
└── workflows-service_service.log
```

## Configuration

Add these environment variables to your main `.env` file:

```env
# MongoDB Configuration for Centralized Logging
MONGODB_URL=mongodb://localhost:27017
MONGODB_DATABASE=agent_logs
MONGODB_COLLECTION=logs
MONGODB_SYNC_INTERVAL=10  # Sync interval in seconds

# Logging Configuration
LOG_BASE_PATH=logs  # Common logs directory
LOG_FILE_EXTENSION=.log
DEBUG_LOGGING=false  # Set to true for debug output
```

## Installation

1. Install the logging service dependencies:
```bash
cd logging-service
pip install -r requirements.txt
```

2. Add the logging service as a dependency in each microservice's `requirements.txt`:
```txt
# Add this line to each service's requirements.txt
../logging-service
```

## Usage in Microservices

### Method 1: Service Logger (Recommended)

```python
# In your microservice (e.g., agent-service)
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'logging-service'))

from logging_service.service_logger import create_service_logger

# Create service-specific logger
logger = create_service_logger("agent-service")

# Use in controllers
def create_agent(db: Session, agent: AgentCreate, session_id: str = ""):
    logger.info("controller", "create_agent", f"Creating new agent: {agent.name}", session_id)
    
    try:
        # ... your code ...
        logger.info("controller", "create_agent", f"Successfully created agent with ID: {new_agent.id}", session_id)
        return new_agent
    except Exception as e:
        logger.error("controller", "create_agent", f"Error creating agent: {str(e)}", session_id)
        raise

# Use in routers
@router.post("/", response_model=AgentOut)
def create(agent: AgentCreate, db: Session = Depends(get_db), current_user = Depends(get_current_user)):
    session_id = f"session-{uuid.uuid4().hex}"
    
    logger.info("router", "create", f"POST /agents/ called by user {current_user.id}", session_id)
    
    try:
        result = crud.create_agent(db, agent, session_id)
        logger.info("router", "create", f"Successfully created agent via API", session_id,
                   metadata={"status_code": 200, "agent_id": result.id})
        return result
    except Exception as e:
        logger.error("router", "create", f"Error in create endpoint: {str(e)}", session_id,
                    metadata={"status_code": 500})
        raise
```

### Method 2: Direct Function Calls

```python
# In your microservice
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'logging-service'))

from logging_service.mongo_logger import log_info, log_error, log_warning

# Use in your code
log_info("agent-service", "controller", "create_agent", "Creating new agent", session_id)
log_error("agent-service", "router", "create", "Access denied", session_id, {"status_code": 403})
```

## Log Format

All services use the same consistent log format:

```json
{
  "_id": "68753f21a9b40966a0bf2d82",
  "source": "router",
  "timestamp": "2025-01-15T10:30:45.123Z",
  "date": "2025-01-15",
  "service": "agent-service",
  "component": "create",
  "level": "INFO",
  "message": "Successfully created agent via API",
  "sessionId": "session-a49db70c6643a30ed043c5a276e0e793",
  "metadata": {
    "status_code": 200,
    "agent_id": 123
  }
}
```

## Service Integration Steps

### For Each Microservice:

1. **Add logging service dependency** to `requirements.txt`:
```txt
../logging-service
```

2. **Remove old logging files**:
   - Delete `app/utils/mongo_logger.py`
   - Delete service-specific `logs/` directory

3. **Update imports** in controllers and routers:
```python
# Replace old import:
# from app.utils.mongo_logger import log_info, log_error

# With new import:
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'logging-service'))
from logging_service.service_logger import create_service_logger

# Create service logger
logger = create_service_logger("your-service-name")
```

4. **Update logging calls**:
```python
# Replace old calls:
# log_info("controller", "create_agent", "Message", session_id)

# With new calls:
# logger.info("controller", "create_agent", "Message", session_id)
```

## Testing

Run the centralized logging test:

```bash
cd logging-service
python test_centralized_logging.py
```

This will test:
- Multi-service logging
- Log format consistency
- MongoDB connection
- Background synchronization

## Benefits

1. **Code Deduplication**: Single logging implementation for all services
2. **Consistency**: Uniform logging format and behavior across all services
3. **Maintainability**: Changes to logging logic only need to be made in one place
4. **Centralized Configuration**: All logging settings in one place
5. **Common Log Storage**: All service logs in a single directory structure
6. **Easy Debugging**: Correlate logs across services using session IDs
7. **Performance**: Optimized background sync reduces impact on service performance

## Migration Guide

See the individual service migration examples in the next section for step-by-step instructions on migrating each microservice to use the centralized logging service.

## Troubleshooting

### Common Issues

1. **Import errors**: Ensure the logging-service path is correctly added to sys.path
2. **Permission errors**: Ensure the logs directory is writable
3. **MongoDB connection**: Verify MongoDB is running and connection string is correct
4. **Missing logs**: Check DEBUG_LOGGING=true to see detailed output

### Debug Mode

Enable debug logging to see detailed output:
```env
DEBUG_LOGGING=true
```

This will show:
- Logger initialization messages
- Log entries as they're written
- Sync operations
- Shutdown messages
