# MongoDB Logging System for Auth Service

## Overview

This logging system provides enterprise-level MongoDB logging with file buffering and background synchronization for the Auth Service. It tracks authentication operations, user management, and API calls with comprehensive session tracking.

## Features

- **File Buffering**: High-performance logging with local file buffering
- **Background Sync**: Automatic MongoDB synchronization at configurable intervals
- **Thread-Safe**: Safe for concurrent use across the application
- **Error Handling**: Graceful handling of MongoDB connection issues
- **Session Tracking**: Complete request tracing with unique session IDs
- **Minimal Integration**: Clean integration with existing auth controller and router code

## Log Format

Logs follow the specified format with auth-service specific components:

```json
{
  "_id": "68753f21a9b40966a0bf2d82",
  "source": "router",
  "timestamp": "2025-01-15T10:30:45.123Z",
  "date": "2025-01-15",
  "service": "auth-service",
  "component": "login",
  "level": "INFO",
  "message": "Login successful: <EMAIL>",
  "sessionId": "session-a49db70c6643a30ed043c5a276e0e793",
  "metadata": {
    "status_code": 200,
    "user_id": 123,
    "roles": ["admin"]
  }
}
```

## Configuration

Add these environment variables to your `.env` file:

```env
# MongoDB Configuration for Logging
MONGODB_URL=mongodb://localhost:27017
MONGODB_DATABASE=agent_logs
MONGODB_COLLECTION=logs
MONGODB_SYNC_INTERVAL=10  # Sync interval in seconds (default: 10)
```

## Dependencies

The system requires `pymongo==4.6.1` which has been added to `requirements.txt`.

## Auth Service Components Logged

### Router Level (`source: "router"`)
- **register**: User registration attempts and results
- **login**: Login attempts, success/failure with credentials validation
- **google_login**: Google OAuth login attempts and user creation
- **refresh_token**: Token refresh operations
- **read_current_user**: User info retrieval requests

### Controller Level (`source: "controller"`)
- **get_user_by_email**: User lookup operations
- **create_user**: User creation with role assignment
- **verify_user**: Password verification and authentication
- **get_user_roles**: Role retrieval operations

## Usage Examples

### Router Logging
```python
# Generate session ID for request tracking
session_id = f"session-{uuid.uuid4().hex}"

# Log API call
log_info("router", "login", f"POST /auth/login called for user: {username}", session_id)

# Log success with metadata
log_info("router", "login", f"Login successful: {username}", session_id,
        metadata={"status_code": 200, "user_id": user.id, "roles": roles})

# Log errors with status codes
log_error("router", "login", f"Login failed - invalid credentials: {username}", session_id,
         metadata={"status_code": 400})
```

### Controller Logging
```python
# Log controller operations
log_info("controller", "create_user", f"Creating new user: {email} with role: {role}", session_id)

# Log successful operations
log_info("controller", "verify_user", f"User verification successful: {email}", session_id)

# Log errors with context
log_error("controller", "create_user", f"Error creating user {email}: {str(e)}", session_id)
```

## Session Tracking

Each API request generates a unique session ID that tracks the complete flow:

1. **Router Entry**: `session-abc123` - API call received
2. **Controller Operations**: `session-abc123` - Database operations
3. **Router Response**: `session-abc123` - Response sent

This allows complete request tracing and debugging.

## Log Levels

- **INFO**: Successful operations, API calls, user actions
- **ERROR**: Authentication failures, database errors, validation errors
- **WARNING**: Security warnings, deprecated operations

## File Structure

```
auth-service/
├── app/
│   └── utils/
│       └── mongo_logger.py          # Main logging utility
├── logs/
│   └── auth_service.log             # Temporary log buffer file
├── test_logging.py                  # Test script
└── LOGGING_README.md               # This file
```

## Testing

Run the test script to verify the logging system:

```bash
cd auth-service
python test_logging.py
```

## Authentication-Specific Logging

### Login Operations
- User authentication attempts
- Password verification results
- JWT token generation
- Role assignment tracking

### Registration Operations
- New user creation
- Email validation
- Role assignment
- Account setup completion

### Google OAuth Operations
- Token verification
- User profile extraction
- Account linking/creation
- OAuth flow completion

### Token Operations
- Token refresh requests
- Token validation
- Expiration handling
- Security token operations

## Security Considerations

- **No Sensitive Data**: Passwords and tokens are never logged
- **User Privacy**: Only necessary user identifiers are logged
- **Session Security**: Session IDs are unique and non-predictable
- **Error Handling**: Security errors are logged without exposing system details

## Performance Impact

- **File I/O**: ~0.1-1ms per log entry (local disk write)
- **MongoDB Sync**: Background operation, no API blocking
- **Memory Usage**: Minimal footprint with immediate file writes
- **Network**: Batch inserts reduce MongoDB connection overhead

## Monitoring

Check the following for system health:
- Log file size in `logs/auth_service.log`
- MongoDB collection document count
- Console messages for sync status
- Authentication success/failure rates

## Troubleshooting

### Common Issues

1. **Logs not syncing to MongoDB**
   - Check MongoDB connection in `.env`
   - Verify MongoDB service is running
   - Check console for connection errors

2. **Missing session IDs**
   - Ensure UUID import is present in router
   - Verify session_id is passed to controller functions

3. **Authentication logs missing**
   - Check that logging imports are added to auth router
   - Verify controller functions accept session_id parameter
