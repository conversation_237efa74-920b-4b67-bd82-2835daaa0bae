from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.database.connection import get_db
from app.controller import project as crud
from app.models.user import User
from app.schemas.project import ProjectCreate, ProjectUpdate, ProjectOut
from app.dependencies.auth_dependencies import get_current_user
from app.utils.permission import user_has_permission


router = APIRouter(prefix="/projects", tags=["Projects"])
# User = Depends(get_current_user)
@router.get("/", response_model=list[ProjectOut])
def read_all(db: Session = Depends(get_db),current_user: User = Depends(get_current_user)):
    if not user_has_permission(current_user.id, "read_project", db):
        raise HTTPException(status_code=403, detail="Access denied")
    return crud.get_all(db)

@router.get("/{project_id}", response_model=ProjectOut)
def read_one(project_id: int, db: Session = Depends(get_db),current_user: User = Depends(get_current_user)):
    if not user_has_permission(current_user.id, "read_project", db):
        raise HTTPException(status_code=403, detail="Access denied")
    project = crud.get_by_id(db, project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    return project

@router.post("/", response_model=ProjectOut)
def create_project(data: ProjectCreate, db: Session = Depends(get_db),current_user: User = Depends(get_current_user)):
    if not user_has_permission(current_user.id, "create_project", db):
        raise HTTPException(status_code=403, detail="Access denied")
    return crud.create(db, data)

@router.put("/{project_id}", response_model=ProjectOut)
def update_project(project_id: int, data: ProjectUpdate, db: Session = Depends(get_db),current_user: User = Depends(get_current_user)):
    if not user_has_permission(current_user.id, "update_project", db):
        raise HTTPException(status_code=403, detail="Access denied")
    updated = crud.update(db, project_id, data)
    if not updated:
        raise HTTPException(status_code=404, detail="Project not found")
    return updated

@router.delete("/{project_id}")
def delete_project(project_id: int, db: Session = Depends(get_db),current_user: User = Depends(get_current_user)):
    if not user_has_permission(current_user.id, "delete_project", db):
        raise HTTPException(status_code=403, detail="Access denied")
    deleted = crud.delete(db, project_id)
    if not deleted:
        raise HTTPException(status_code=404, detail="Project not found")
    return {"message": "Project deleted successfully"}