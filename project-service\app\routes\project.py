import uuid
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.database.connection import get_db
from app.controller import project as crud
from app.models.user import User
from app.schemas.project import ProjectCreate, ProjectUpdate, ProjectOut
from app.dependencies.auth_dependencies import get_current_user
from app.utils.permission import user_has_permission
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'logging-service'))
from logging_service.service_logger import create_service_logger

# Create service-specific logger
logger = create_service_logger("project-service")


router = APIRouter(prefix="/projects", tags=["Projects"])
# User = Depends(get_current_user)
@router.get("/", response_model=list[ProjectOut])
def read_all(db: Session = Depends(get_db),current_user: User = Depends(get_current_user)):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        logger.info("router", "read_all", f"GET /projects/ called by user {current_user.id}", session_id)

        if not user_has_permission(current_user.id, "read_project", db):
            logger.error("router", "read_all", f"Access denied for user {current_user.id}", session_id,
                     metadata={"status_code": 403})
            raise HTTPException(status_code=403, detail="Access denied")

        result = crud.get_all(db, session_id)
        logger.info("router", "read_all", f"Successfully retrieved projects via API", session_id,
                metadata={"status_code": 200, "project_count": len(result)})
        return result

    except HTTPException as e:
        logger.error("router", "read_all", f"HTTP error in read_all: {e.detail}", session_id,
                 metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        logger.error("router", "read_all", f"Unexpected error in read_all: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/{project_id}", response_model=ProjectOut)
def read_one(project_id: int, db: Session = Depends(get_db),current_user: User = Depends(get_current_user)):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        logger.info("router", "read_one", f"GET /projects/{project_id} called by user {current_user.id}", session_id)

        if not user_has_permission(current_user.id, "read_project", db):
            logger.error("router", "read_one", f"Access denied for user {current_user.id}", session_id,
                     metadata={"status_code": 403})
            raise HTTPException(status_code=403, detail="Access denied")

        project = crud.get_by_id(db, project_id, session_id)
        if not project:
            logger.error("router", "read_one", f"Project {project_id} not found", session_id,
                     metadata={"status_code": 404})
            raise HTTPException(status_code=404, detail="Project not found")

        logger.info("router", "read_one", f"Successfully retrieved project {project_id} via API", session_id,
                metadata={"status_code": 200, "project_name": project.name})
        return project

    except HTTPException as e:
        logger.error("router", "read_one", f"HTTP error in read_one: {e.detail}", session_id,
                 metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        logger.error("router", "read_one", f"Unexpected error in read_one: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/", response_model=ProjectOut)
def create_project(data: ProjectCreate, db: Session = Depends(get_db),current_user: User = Depends(get_current_user)):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        logger.info("router", "create_project", f"POST /projects/ called by user {current_user.id}", session_id)

        if not user_has_permission(current_user.id, "create_project", db):
            logger.error("router", "create_project", f"Access denied for user {current_user.id}", session_id,
                     metadata={"status_code": 403})
            raise HTTPException(status_code=403, detail="Access denied")

        result = crud.create(db, data, session_id)
        logger.info("router", "create_project", f"Successfully created project via API", session_id,
                metadata={"status_code": 200, "project_id": result.id})
        return result

    except HTTPException as e:
        logger.error("router", "create_project", f"HTTP error in create_project: {e.detail}", session_id,
                 metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        logger.error("router", "create_project", f"Unexpected error in create_project: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")

@router.put("/{project_id}", response_model=ProjectOut)
def update_project(project_id: int, data: ProjectUpdate, db: Session = Depends(get_db),current_user: User = Depends(get_current_user)):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        logger.info("router", "update_project", f"PUT /projects/{project_id} called by user {current_user.id}", session_id)

        if not user_has_permission(current_user.id, "update_project", db):
            logger.error("router", "update_project", f"Access denied for user {current_user.id}", session_id,
                     metadata={"status_code": 403})
            raise HTTPException(status_code=403, detail="Access denied")

        updated = crud.update(db, project_id, data, session_id)
        if not updated:
            logger.error("router", "update_project", f"Project {project_id} not found for update", session_id,
                     metadata={"status_code": 404})
            raise HTTPException(status_code=404, detail="Project not found")

        logger.info("router", "update_project", f"Successfully updated project {project_id} via API", session_id,
                metadata={"status_code": 200, "project_name": updated.name})
        return updated

    except HTTPException as e:
        logger.error("router", "update_project", f"HTTP error in update_project: {e.detail}", session_id,
                 metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        logger.error("router", "update_project", f"Unexpected error in update_project: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")

@router.delete("/{project_id}")
def delete_project(project_id: int, db: Session = Depends(get_db),current_user: User = Depends(get_current_user)):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        logger.info("router", "delete_project", f"DELETE /projects/{project_id} called by user {current_user.id}", session_id)

        if not user_has_permission(current_user.id, "delete_project", db):
            logger.error("router", "delete_project", f"Access denied for user {current_user.id}", session_id,
                     metadata={"status_code": 403})
            raise HTTPException(status_code=403, detail="Access denied")

        deleted = crud.delete(db, project_id, session_id)
        if not deleted:
            logger.error("router", "delete_project", f"Project {project_id} not found for deletion", session_id,
                     metadata={"status_code": 404})
            raise HTTPException(status_code=404, detail="Project not found")

        logger.info("router", "delete_project", f"Successfully deleted project {project_id} via API", session_id,
                metadata={"status_code": 200})
        return {"message": "Project deleted successfully"}

    except HTTPException as e:
        logger.error("router", "delete_project", f"HTTP error in delete_project: {e.detail}", session_id,
                 metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        logger.error("router", "delete_project", f"Unexpected error in delete_project: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")