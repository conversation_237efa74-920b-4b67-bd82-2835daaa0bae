from sqlalchemy import Column, String,BigInteger,TIMESTAMP,Text,Integer,DateTime
from app.database.connection import Base
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship


class Organization(Base):
    __tablename__ = "organizations"

    id = Column(Integer, primary_key=True)
    name = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())