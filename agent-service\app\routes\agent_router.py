import uuid
from sqlalchemy.orm import Session
from app.schemas.agents import <PERSON><PERSON><PERSON>, Agent<PERSON><PERSON>, AgentUpdate
from app.controller.agents import get_agent, get_agents, create_agent, delete_agent, update_agent
from app.utils.permissions import user_has_permission
from app.dependencies.auth_dependencies import get_current_user
from app.database.connection import get_db
from app.utils.mongo_logger import log_info, log_error
from fastapi import APIRouter, Depends, HTTPException

# ✅ Create a router with a prefix and tags for Swagger documentation
router = APIRouter(prefix="/agents", tags=["agents"])

# ✅ Route: Create a new agent
@router.post("/agents/", response_model=AgentOut)
def create_agents(
    agent: AgentCreate,
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        log_info("router", "create_agents", f"POST /agents/ called by user {current_user.id}", session_id)

        # Permission check for creating an agent
        if not user_has_permission(current_user.id, "create_agent", db):
            log_error("router", "create_agents", f"Access denied for user {current_user.id}", session_id,
                     metadata={"status_code": 403})
            raise HTTPException(status_code=403, detail="Access denied")

        result = create_agent(db, agent, session_id)
        log_info("router", "create_agents", f"Successfully created agent via API", session_id,
                metadata={"status_code": 200, "agent_id": result.id})
        return result

    except HTTPException as e:
        log_error("router", "create_agents", f"HTTP error in create_agents: {e.detail}", session_id,
                 metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        log_error("router", "create_agents", f"Unexpected error in create_agents: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")

# ✅ Route: Get a list of all agents
@router.get("/agents/", response_model=list[AgentOut])
def read_agents(
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        log_info("router", "read_agents", f"GET /agents/ called by user {current_user.id}", session_id)

        # Permission check for reading agents
        if not user_has_permission(current_user.id, "read_agent", db):
            log_error("router", "read_agents", f"Access denied for user {current_user.id}", session_id,
                     metadata={"status_code": 403})
            raise HTTPException(status_code=403, detail="Access denied")

        result = get_agents(db, session_id)
        log_info("router", "read_agents", f"Successfully retrieved agents via API", session_id,
                metadata={"status_code": 200, "agent_count": len(result)})
        return result

    except HTTPException as e:
        log_error("router", "read_agents", f"HTTP error in read_agents: {e.detail}", session_id,
                 metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        log_error("router", "read_agents", f"Unexpected error in read_agents: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")

# ✅ Route: Get a single agent by ID (no auth check – public access or assumed internal use)
@router.get("/agents/{agent_id}", response_model=AgentOut)
def read_agent_by_id(agent_id: int, db: Session = Depends(get_db)):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        log_info("router", "read_agent_by_id", f"GET /agents/{agent_id} called", session_id)

        db_agent = get_agent(db, agent_id, session_id)
        if db_agent is None:
            log_error("router", "read_agent_by_id", f"Agent {agent_id} not found", session_id,
                     metadata={"status_code": 404})
            raise HTTPException(status_code=404, detail="Agent not found")

        log_info("router", "read_agent_by_id", f"Successfully retrieved agent {agent_id} via API", session_id,
                metadata={"status_code": 200, "agent_name": db_agent.name})
        return db_agent

    except HTTPException as e:
        log_error("router", "read_agent_by_id", f"HTTP error in read_agent_by_id: {e.detail}", session_id,
                 metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        log_error("router", "read_agent_by_id", f"Unexpected error in read_agent_by_id: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")

# ✅ Route: Update an existing agent
@router.put("/agents/{agent_id}", response_model=AgentOut)
def update_agent_route(
    agent_id: int,
    updated_data: AgentUpdate,
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        log_info("router", "update_agent_route", f"PUT /agents/{agent_id} called by user {current_user.id}", session_id)

        # Permission check for updating agent
        if not user_has_permission(current_user.id, "update_agent", db):
            log_error("router", "update_agent_route", f"Access denied for user {current_user.id}", session_id,
                     metadata={"status_code": 403})
            raise HTTPException(status_code=403, detail="Access denied")

        updated = update_agent(db, agent_id, updated_data, session_id)
        if not updated:
            log_error("router", "update_agent_route", f"Agent {agent_id} not found for update", session_id,
                     metadata={"status_code": 404})
            raise HTTPException(status_code=404, detail="Agent not found")

        log_info("router", "update_agent_route", f"Successfully updated agent {agent_id} via API", session_id,
                metadata={"status_code": 200, "agent_name": updated.name})
        return updated

    except HTTPException as e:
        log_error("router", "update_agent_route", f"HTTP error in update_agent_route: {e.detail}", session_id,
                 metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        log_error("router", "update_agent_route", f"Unexpected error in update_agent_route: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")

# ✅ Route: Delete an agent
@router.delete("/agents/{agent_id}")
def delete_agent_route(
    agent_id: int,
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        log_info("router", "delete_agent_route", f"DELETE /agents/{agent_id} called by user {current_user.id}", session_id)

        # Permission check for deleting agent
        if not user_has_permission(current_user.id, "delete_agent", db):
            log_error("router", "delete_agent_route", f"Access denied for user {current_user.id}", session_id,
                     metadata={"status_code": 403})
            raise HTTPException(status_code=403, detail="Access denied")

        deleted = delete_agent(db, agent_id, session_id)
        if not deleted:
            log_error("router", "delete_agent_route", f"Agent {agent_id} not found for deletion", session_id,
                     metadata={"status_code": 404})
            raise HTTPException(status_code=404, detail="Agent not found")

        log_info("router", "delete_agent_route", f"Successfully deleted agent {agent_id} via API", session_id,
                metadata={"status_code": 200})
        return {"detail": "Agent deleted successfully"}

    except HTTPException as e:
        log_error("router", "delete_agent_route", f"HTTP error in delete_agent_route: {e.detail}", session_id,
                 metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        log_error("router", "delete_agent_route", f"Unexpected error in delete_agent_route: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")
