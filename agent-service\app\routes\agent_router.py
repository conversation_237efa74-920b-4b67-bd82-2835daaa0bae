from sqlalchemy.orm import Session
from app.schemas.agents import <PERSON><PERSON><PERSON>, Agent<PERSON><PERSON>, AgentUpdate
from app.controller.agents import get_agent, get_agents, create_agent, delete_agent, update_agent
from app.utils.permissions import user_has_permission
from app.dependencies.auth_dependencies import get_current_user
from app.database.connection import get_db
from fastapi import APIRouter, Depends, HTTPException

# ✅ Create a router with a prefix and tags for Swagger documentation
router = APIRouter(prefix="/agents", tags=["agents"])

# ✅ Route: Create a new agent
@router.post("/agents/", response_model=AgentOut)
def create_agents(
    agent: AgentCreate,
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    # Permission check for creating an agent
    if not user_has_permission(current_user.id, "create_agent", db):
        raise HTTPException(status_code=403, detail="Access denied")
    
    return create_agent(db, agent)

# ✅ Route: Get a list of all agents
@router.get("/agents/", response_model=list[AgentOut])
def read_agents(
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    # Permission check for reading agents
    if not user_has_permission(current_user.id, "read_agent", db):
        raise HTTPException(status_code=403, detail="Access denied")
    
    return get_agents(db)

# ✅ Route: Get a single agent by ID (no auth check – public access or assumed internal use)
@router.get("/agents/{agent_id}", response_model=AgentOut)
def read_agents(agent_id: int, db: Session = Depends(get_db)):
    db_agent = get_agent(db, agent_id)
    if db_agent is None:
        raise HTTPException(status_code=404, detail="Agent not found")
    
    return db_agent

# ✅ Route: Update an existing agent
@router.put("/agents/{agent_id}", response_model=AgentOut)
def update_agent_route(
    agent_id: int,
    updated_data: AgentUpdate,
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    # Permission check for updating agent
    if not user_has_permission(current_user.id, "update_agent", db):
        raise HTTPException(status_code=403, detail="Access denied")
    
    updated = update_agent(db, agent_id, updated_data)
    if not updated:
        raise HTTPException(status_code=404, detail="Agent not found")
    
    return updated

# ✅ Route: Delete an agent
@router.delete("/agents/{agent_id}")
def delete_agent_route(
    agent_id: int,
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    # Permission check for deleting agent
    if not user_has_permission(current_user.id, "delete_agent", db):
        raise HTTPException(status_code=403, detail="Access denied")
    
    deleted = delete_agent(db, agent_id)
    if not deleted:
        raise HTTPException(status_code=404, detail="Agent not found")
    
    return {"detail": "Agent deleted successfully"}
