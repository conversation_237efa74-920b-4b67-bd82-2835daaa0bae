"""
Test script for Centralized MongoDB Logging Service

This script tests the centralized logging system to ensure:
1. Multiple services can log simultaneously
2. Service-specific log files are created
3. Logs are properly buffered and synced to MongoDB
4. Log format is correct across all services
5. Background threads work properly for all services
"""

import time
import json
from pathlib import Path
from service_logger import create_service_logger
from mongo_logger import shutdown_all_loggers


def test_multi_service_logging():
    """Test logging from multiple services simultaneously"""
    print("Testing multi-service logging...")
    
    # Create loggers for different services
    agent_logger = create_service_logger("agent-service")
    auth_logger = create_service_logger("auth-service")
    org_logger = create_service_logger("organization-service")
    project_logger = create_service_logger("project-service")
    workflow_logger = create_service_logger("workflows-service")
    
    # Generate test logs from different services
    agent_logger.info("test", "multi_service", "Agent service test log", "session-agent-123")
    auth_logger.info("test", "multi_service", "Auth service test log", "session-auth-456")
    org_logger.error("test", "multi_service", "Organization service error log", "session-org-789")
    project_logger.warning("test", "multi_service", "Project service warning log", "session-proj-101")
    workflow_logger.info("test", "multi_service", "Workflow service test log", "session-wf-202")
    
    # Check if service-specific log files are created
    log_dir = Path("../logs")
    expected_files = [
        "agent-service_service.log",
        "auth-service_service.log", 
        "organization-service_service.log",
        "project-service_service.log",
        "workflows-service_service.log"
    ]
    
    time.sleep(0.2)  # Allow time for file writes
    
    created_files = []
    for file_name in expected_files:
        file_path = log_dir / file_name
        if file_path.exists() and file_path.stat().st_size > 0:
            created_files.append(file_name)
            print(f"   ✅ {file_name} created successfully")
        else:
            print(f"   ❌ {file_name} not found or empty")
    
    if len(created_files) == len(expected_files):
        print("✅ Multi-service logging works - all service log files created")
    else:
        print(f"⚠️  Multi-service logging partial - {len(created_files)}/{len(expected_files)} files created")


def test_log_format_consistency():
    """Test that log format is consistent across all services"""
    print("\nTesting log format consistency...")
    
    log_dir = Path("../logs")
    services = ["agent-service", "auth-service", "organization-service", "project-service", "workflows-service"]
    
    format_consistent = True
    
    for service in services:
        log_file = log_dir / f"{service}_service.log"
        if log_file.exists():
            try:
                with open(log_file, 'r') as f:
                    lines = f.readlines()
                    if lines:
                        log_entry = json.loads(lines[0].strip())
                        
                        # Check required fields
                        required_fields = ["_id", "source", "timestamp", "date", "service", 
                                         "component", "level", "message", "sessionId", "metadata"]
                        
                        missing_fields = [field for field in required_fields if field not in log_entry]
                        
                        if not missing_fields and log_entry.get('service') == service:
                            print(f"   ✅ {service}: Format correct")
                        else:
                            print(f"   ❌ {service}: Format incorrect - missing: {missing_fields}")
                            format_consistent = False
                            
            except (json.JSONDecodeError, FileNotFoundError) as e:
                print(f"   ❌ {service}: Error reading log - {str(e)}")
                format_consistent = False
    
    if format_consistent:
        print("✅ Log format is consistent across all services")
    else:
        print("❌ Log format inconsistencies found")


def test_background_sync():
    """Test that background sync works for all services"""
    print("\nTesting background sync for all services...")
    
    # Generate more logs from multiple services
    services = ["agent-service", "auth-service", "organization-service", "project-service", "workflows-service"]
    
    for service in services:
        logger = create_service_logger(service)
        for i in range(2):
            logger.info("test", "background_sync", f"{service} sync test message {i+1}", f"sync-{service}-{i}")
    
    print("Generated test logs from all services, waiting for background sync...")
    
    # Get sync interval from config
    try:
        from config import MONGODB_SYNC_INTERVAL
        sync_interval = MONGODB_SYNC_INTERVAL
    except:
        sync_interval = 10  # Default fallback
    
    print(f"Note: Background sync runs every {sync_interval} seconds")
    print("Check MongoDB collection 'logs' in database 'agent_logs' for synced logs")
    
    # Wait for sync to happen
    time.sleep(sync_interval + 2)
    
    # Check if log files were cleared (indicating successful sync)
    log_dir = Path("../logs")
    synced_services = []
    
    for service in services:
        log_file = log_dir / f"{service}_service.log"
        if log_file.exists():
            file_size = log_file.stat().st_size
            if file_size == 0:
                synced_services.append(service)
                print(f"   ✅ {service}: Log file cleared (sync successful)")
            else:
                print(f"   ⚠️  {service}: Log file still has content (sync may have failed)")
        else:
            print(f"   ⚠️  {service}: Log file doesn't exist")
    
    if len(synced_services) == len(services):
        print("✅ Background sync appears to be working for all services")
    else:
        print(f"⚠️  Background sync working for {len(synced_services)}/{len(services)} services")


def test_mongodb_connection():
    """Test MongoDB connection"""
    print("\nTesting MongoDB connection...")
    
    try:
        from pymongo import MongoClient
        from config import MONGODB_URL, MONGODB_DATABASE
        
        client = MongoClient(MONGODB_URL, serverSelectionTimeoutMS=5000)
        # Test connection
        client.admin.command('ping')
        
        db = client[MONGODB_DATABASE]
        collections = db.list_collection_names()
        
        print("✅ MongoDB connection successful")
        print(f"   Database: {MONGODB_DATABASE}")
        print(f"   Collections: {collections}")
        
        client.close()
        
    except Exception as e:
        print(f"❌ MongoDB connection failed: {str(e)}")
        print("   Make sure MongoDB is running and connection string is correct")


def main():
    """Run all tests"""
    print("🧪 Testing Centralized MongoDB Logging System")
    print("=" * 70)
    
    test_multi_service_logging()
    test_log_format_consistency()
    test_mongodb_connection()
    test_background_sync()
    
    print("\n" + "=" * 70)
    print("Test completed. Check the results above.")
    print("\nTo verify centralized MongoDB logging:")
    print("1. Ensure MongoDB is running")
    print("2. Check the 'agent_logs' database")
    print("3. Look for documents in the 'logs' collection")
    print("4. Filter by different service names to see multi-service logs")
    
    # Keep the script running for a bit to allow background sync
    print("\nKeeping script running for 15 seconds to allow background sync...")
    time.sleep(15)
    
    # Shutdown all loggers gracefully
    shutdown_all_loggers()
    print("All loggers shutdown complete.")


if __name__ == "__main__":
    main()
