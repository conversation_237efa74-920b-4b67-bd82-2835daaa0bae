from sqlalchemy.orm import Session, joinedload
from sqlalchemy.exc import SQLAlchemyError
from fastapi import HTTPException

from app.models.workflow import Workflow
from app.schemas.workflow import WorkflowCreate, WorkflowUpdate

# 🔹 Create a new workflow entry in the database
def create_workflow(db: Session, workflow: WorkflowCreate):
    try:
        new_workflow = Workflow(**workflow.dict())
        db.add(new_workflow)
        db.commit()
        db.refresh(new_workflow)  # Refresh to get generated ID and timestamps
        return new_workflow
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f" Failed to create workflow: {str(e)}")

# 📄 Fetch all workflows with their creators
def get_all_workflows(db: Session):
    try:
        return db.query(Workflow).options(joinedload(Workflow.creator)).all()
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f" Failed to fetch workflows: {str(e)}")

# 🔍 Get a specific workflow by ID with creator info
def get_workflow_by_id(db: Session, workflow_id: int):
    try:
        return db.query(Workflow).options(joinedload(Workflow.creator)).filter(Workflow.id == workflow_id).first()
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f" Failed to retrieve workflow: {str(e)}")

# ✏️ Update a workflow's fields
def update_workflow(db: Session, workflow_id: int, data: WorkflowUpdate):
    try:
        workflow = db.query(Workflow).filter(Workflow.id == workflow_id).first()
        if not workflow:
            return None
        for key, value in data.dict(exclude_unset=True).items():
            setattr(workflow, key, value)
        db.commit()
        db.refresh(workflow)
        return workflow
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f" Failed to update workflow: {str(e)}")

# 🗑️ Delete a workflow by ID
def delete_workflow(db: Session, workflow_id: int):
    try:
        workflow = db.query(Workflow).filter(Workflow.id == workflow_id).first()
        if workflow:
            db.delete(workflow)
            db.commit()
            return True
        return False
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f" Failed to delete workflow: {str(e)}")
