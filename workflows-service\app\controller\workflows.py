from sqlalchemy.orm import Session, joinedload
from sqlalchemy.exc import SQLAlchemyError
from fastapi import HTTPException

from app.models.workflow import Workflow
from app.schemas.workflow import WorkflowCreate, WorkflowUpdate
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'logging-service'))
from logging_service.service_logger import create_service_logger

# Create service-specific logger
logger = create_service_logger("workflows-service")

# 🔹 Create a new workflow entry in the database
def create_workflow(db: Session, workflow: WorkflowCreate, session_id: str = ""):
    try:
        logger.info("controller", "create_workflow", f"Creating new workflow: {workflow.name}", session_id)

        new_workflow = Workflow(**workflow.dict())
        db.add(new_workflow)
        db.commit()
        db.refresh(new_workflow)  # Refresh to get generated ID and timestamps

        logger.info("controller", "create_workflow", f"Successfully created workflow with ID: {new_workflow.id}", session_id)
        return new_workflow
    except SQLAlchemyError as e:
        db.rollback()
        logger.error("controller", "create_workflow", f"Database error creating workflow: {str(e)}", session_id)
        raise HTTPException(status_code=500, detail=f" Failed to create workflow: {str(e)}")

# 📄 Fetch all workflows with their creators
def get_all_workflows(db: Session, session_id: str = ""):
    try:
        logger.info("controller", "get_all_workflows", "Retrieving all workflows", session_id)

        workflows = db.query(Workflow).options(joinedload(Workflow.creator)).all()

        logger.info("controller", "get_all_workflows", f"Successfully retrieved {len(workflows)} workflows", session_id)
        return workflows
    except SQLAlchemyError as e:
        logger.error("controller", "get_all_workflows", f"Database error retrieving workflows: {str(e)}", session_id)
        raise HTTPException(status_code=500, detail=f" Failed to fetch workflows: {str(e)}")

# 🔍 Get a specific workflow by ID with creator info
def get_workflow_by_id(db: Session, workflow_id: int, session_id: str = ""):
    try:
        logger.info("controller", "get_workflow_by_id", f"Retrieving workflow with ID: {workflow_id}", session_id)

        workflow = db.query(Workflow).options(joinedload(Workflow.creator)).filter(Workflow.id == workflow_id).first()

        if workflow:
            logger.info("controller", "get_workflow_by_id", f"Successfully retrieved workflow: {workflow.name}", session_id)
        else:
            logger.info("controller", "get_workflow_by_id", f"Workflow with ID {workflow_id} not found", session_id)

        return workflow
    except SQLAlchemyError as e:
        logger.error("controller", "get_workflow_by_id", f"Database error retrieving workflow {workflow_id}: {str(e)}", session_id)
        raise HTTPException(status_code=500, detail=f" Failed to retrieve workflow: {str(e)}")

# ✏️ Update a workflow's fields
def update_workflow(db: Session, workflow_id: int, data: WorkflowUpdate, session_id: str = ""):
    try:
        logger.info("controller", "update_workflow", f"Updating workflow with ID: {workflow_id}", session_id)

        workflow = db.query(Workflow).filter(Workflow.id == workflow_id).first()
        if not workflow:
            logger.info("controller", "update_workflow", f"Workflow with ID {workflow_id} not found for update", session_id)
            return None

        updated_fields = data.dict(exclude_unset=True)
        for key, value in updated_fields.items():
            setattr(workflow, key, value)
        db.commit()
        db.refresh(workflow)

        logger.info("controller", "update_workflow", f"Successfully updated workflow {workflow_id}: {list(updated_fields.keys())}", session_id)
        return workflow
    except SQLAlchemyError as e:
        db.rollback()
        logger.error("controller", "update_workflow", f"Database error updating workflow {workflow_id}: {str(e)}", session_id)
        raise HTTPException(status_code=500, detail=f" Failed to update workflow: {str(e)}")

# 🗑️ Delete a workflow by ID
def delete_workflow(db: Session, workflow_id: int, session_id: str = ""):
    try:
        logger.info("controller", "delete_workflow", f"Deleting workflow with ID: {workflow_id}", session_id)

        workflow = db.query(Workflow).filter(Workflow.id == workflow_id).first()
        if workflow:
            workflow_name = workflow.name  # Store name for logging before deletion
            db.delete(workflow)
            db.commit()

            logger.info("controller", "delete_workflow", f"Successfully deleted workflow {workflow_id}: {workflow_name}", session_id)
            return True
        else:
            logger.info("controller", "delete_workflow", f"Workflow with ID {workflow_id} not found for deletion", session_id)
            return False
    except SQLAlchemyError as e:
        db.rollback()
        logger.error("controller", "delete_workflow", f"Database error deleting workflow {workflow_id}: {str(e)}", session_id)
        raise HTTPException(status_code=500, detail=f" Failed to delete workflow: {str(e)}")
