from datetime import datetime, timedelta
from jose import JWTError, jwt # jose is used for encoding and decoding JWT tokens
from app.config import SECRET_KEY, ALGORITHM, ACCESS_TOKEN_EXPIRE_MINUTES  # App-wide config variables
from decouple import config   # For securely loading environment variables

# Function: decode_access_token
# Purpose: To verify and decode a JWT access token


# Decodes a JWT using your SECRET_KEY and ALGORITHM.
def decode_access_token(token: str):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload # Return the payload if token is valid
    except JWTError:  # If decoding fails (invalid/expired), return None
        return None
    

    # Function: create_access_token
# Purpose: To create a JWT access token with expiry
# Params:
#   - data: dict => payload to encode (e.g., {"sub": user_id})
#   - expires_delta: timedelta => optional expiry time

# Creates a JWT access token with a custom or default expiry.
def create_access_token(data: dict, expires_delta: timedelta = None):
    to_encode = data.copy()  # Create a copy of the payload
    # Set expiration time: default from .env if not provided
    expire = datetime.utcnow() + (expires_delta or timedelta(minutes=int(config("ACCESS_TOKEN_EXPIRE_MINUTES"))))
    to_encode.update({"exp": expire}) # Add expiry to the payload
    # Encode the token and return it
    return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

# Function: create_refresh_token
# Purpose: To create a longer-lived refresh token
# Usually used to get a new access token without logging in again

# Creates a longer-lived refresh token (default expiry: days).
def create_refresh_token(data: dict):
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=int(config("REFRESH_TOKEN_EXPIRE_DAYS")))
    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)


