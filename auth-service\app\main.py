from fastapi import FastAPI
from app.routes.auth_router import router as auth_router
from app.database.connection import Base, engine

app = FastAPI(title="Auth Service", version="1.0.0")

# Create database tables
Base.metadata.create_all(bind=engine)

# Include routers
app.include_router(auth_router)

@app.get("/health")
def health_check():
    return {"status": "healthy", "service": "auth-service"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
