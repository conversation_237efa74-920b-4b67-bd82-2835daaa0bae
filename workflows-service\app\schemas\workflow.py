from pydantic import BaseModel,ConfigDict
from datetime import datetime
from typing import Optional
from enum import Enum

class WorkflowStatus(str, Enum):
    pending = "pending"
    approved = "approved"
    rejected = "rejected"

class WorkflowBase(BaseModel):
    organization_id: int
    created_by: int
    name: str
    status: WorkflowStatus = WorkflowStatus.pending

class WorkflowCreate(WorkflowBase):
    pass

class WorkflowUpdate(BaseModel):
    name: Optional[str] = None
    status: Optional[str] = None

class UserBase(BaseModel):
    id: int
    name: str
    email: str
    model_config = ConfigDict(from_attributes=True)


class WorkflowOut(BaseModel):
    id: int
    organization_id: int
    name: str
    status: str
    created_at: datetime
    updated_at: Optional[datetime]

    creator: UserBase  # ✅ match SQLAlchemy's attribute name

    model_config = ConfigDict(from_attributes=True)
