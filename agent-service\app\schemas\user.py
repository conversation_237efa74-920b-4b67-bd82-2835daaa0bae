from pydantic import BaseModel, EmailStr
from typing import List, Optional

# ✅ Schema used when registering or creating a new user
class UserCreate(BaseModel):
    name: str                             # User's full name
    email: EmailStr                       # Validated email address
    password: str                         # Raw password (to be hashed before saving)
    organization_id: int                  # ID of the organization the user belongs to

# ✅ Schema used to return basic user information (e.g., in responses)
class UserOut(BaseModel):
    id: int                               # Unique user ID
    name: str
    email: EmailStr
    is_account_holder: bool               # Whether user is the owner of the org account

    class Config:
        orm_mode = True                   # ✅ Enables SQLAlchemy model compatibility

# ✅ Schema representing the JWT access token
class Token(BaseModel):
    access_token: str                     # The JWT token string
    token_type: str                       # Typically 'bearer'

# ✅ Schema returned after successful registration
class RegisterResponse(BaseModel):
    message: str                          # Success message (e.g., "User registered successfully")
    access_token: str                     # Issued access token
    token_type: str                       # Typically 'bearer'

# ✅ Schema used when logging in a user
class Login(BaseModel):
    # name: str  → You commented this out, likely not required for login
    email: EmailStr                       # User's email
    password: str                         # User's password

# ✅ Schema used when refreshing an access token
class RefreshTokenRequest(BaseModel):
    refresh_token: str                    # JWT refresh token

# ✅ Extended user info, usually used in profile or dashboard responses
class UserInfo(BaseModel):
    id: int
    name: str
    email: str
    organization_id: Optional[int]        # Optional in case user isn't tied to an organization
    roles: List[str]                      # List of roles assigned to the user

# ✅ Schema used when updating a user’s details (PATCH/PUT)
class UserUpdate(BaseModel):
    name: Optional[str] = None            # Fields are optional for partial updates
    email: Optional[str] = None
    organization_id: Optional[int] = None
