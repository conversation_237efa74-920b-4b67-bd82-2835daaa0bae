
from sqlalchemy.orm import Session
from app.models.premission import Permission
from app.models.role_premission import RolePermission
from app.models.user import UserRole

def user_has_permission(user_id: int, permission_name: str, db: Session) -> bool:

    result = (
        db.query(Permission)
        .join(RolePermission, Permission.id == RolePermission.permission_id)
        .join(UserRole, RolePermission.role_id == UserRole.role_id)
        .filter(UserRole.user_id == user_id, Permission.name == permission_name)
        .first()
    )
    return result is not None
