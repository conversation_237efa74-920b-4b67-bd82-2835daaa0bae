import pytest
from fastapi.testclient import Test<PERSON>lient
from app.main import app
from app.database.connection import Session<PERSON>ocal
from app.models.user import User, Role, UserRole
from app.models.organization import Organization
from sqlalchemy.orm import Session
from app.controllers.user import create_user
from app.controllers.organization_controller import create as create_organization_controller
from app.schemas.user import UserCreate
from app.schemas.organization import OrganizationCreate
from app.auth.jwt_handler import create_access_token
from app.database.connection import get_db
import uuid

client = TestClient(app)

# Override get_db dependency for tests
def override_get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Fix the dependency override
app.dependency_overrides[get_db] = override_get_db

# Fixtures
@pytest.fixture(scope="function")
def db_session():
    db = SessionLocal()
    try:
        # Start a savepoint transaction for better isolation
        db.begin()
        yield db
        db.rollback()  # Rollback after each test
    finally:
        db.close()

@pytest.fixture(scope="function")
def test_user(db_session):
    # Use a unique email for each test run
    email = f"testuser_{uuid.uuid4().hex[:8]}@example.com"
    
    user_data = UserCreate(
        name="Test User",
        email=email,
        password="password123",
        organization_id=1
    )
    user = create_user(db_session, user_data, default_role="user")
    db_session.commit()
    return user

@pytest.fixture(scope="function")
def admin_user(db_session):
    # Use a unique email for each test run
    email = f"admin_{uuid.uuid4().hex[:8]}@example.com"
    
    user_data = UserCreate(
        name="Admin User",
        email=email,
        password="adminpass",
        organization_id=1
    )
    user = create_user(db_session, user_data, default_role="admin")
    db_session.commit()
    return user

@pytest.fixture(scope="function")
def user_token(test_user):
    roles = ["user"]
    access_token = create_access_token(data={"sub": test_user.email, "roles": roles})
    return access_token

@pytest.fixture(scope="function")
def admin_token(admin_user):
    roles = ["admin"]
    access_token = create_access_token(data={"sub": admin_user.email, "roles": roles})
    return access_token

@pytest.fixture(scope="function")
def test_organization(db_session):
    org_data = OrganizationCreate(
        name=f"Test Organization {uuid.uuid4().hex[:8]}",
        description="This is a test organization",
        # Add other required fields based on your OrganizationCreate schema
    )
    organization = create_organization_controller(db_session, org_data)
    db_session.commit()
    return organization


# Test GET /organizations/ - Get all organizations
def test_get_all_organizations_with_user_token(user_token):
    headers = {"Authorization": f"Bearer {user_token}"}
    response = client.get("/organizations/", headers=headers)
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)

def test_get_all_organizations_with_admin_token(admin_token):
    headers = {"Authorization": f"Bearer {admin_token}"}
    response = client.get("/organizations/", headers=headers)
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)

def test_get_all_organizations_without_token():
    response = client.get("/organizations/")
    assert response.status_code in [401, 403]
    assert response.json()["detail"] == "Not authenticated"

def test_get_all_organizations_with_invalid_token():
    headers = {"Authorization": "Bearer invalid-token"}
    response = client.get("/organizations/", headers=headers)
    assert response.status_code == 401
    assert response.json()["detail"] == "Could not validate credentials"

# Test GET /organizations/{org_id} - Get organization by ID
def test_get_organization_by_id_success(user_token, test_organization):
    headers = {"Authorization": f"Bearer {user_token}"}
    response = client.get(f"/organizations/{test_organization.id}", headers=headers)
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_organization.id
    assert data["name"] == test_organization.name

def test_get_organization_by_id_not_found(user_token):
    headers = {"Authorization": f"Bearer {user_token}"}
    response = client.get("/organizations/99999", headers=headers)
    assert response.status_code == 404
    assert response.json()["detail"] == "Organization not found"

def test_get_organization_by_id_without_token(test_organization):
    response = client.get(f"/organizations/{test_organization.id}")
    assert response.status_code in [401, 403]
    assert response.json()["detail"] == "Not authenticated"

def test_get_organization_by_id_with_invalid_token(test_organization):
    headers = {"Authorization": "Bearer invalid-token"}
    response = client.get(f"/organizations/{test_organization.id}", headers=headers)
    assert response.status_code == 401
    assert response.json()["detail"] == "Could not validate credentials"

# Test POST /organizations/ - Create organization
def test_create_organization_success(user_token):
    headers = {"Authorization": f"Bearer {user_token}"}
    org_data = {
        "name": f"New Organization {uuid.uuid4().hex[:8]}",
        "description": "A new test organization",
        # Add other required fields based on your OrganizationCreate schema
    }
    response = client.post("/organizations/", json=org_data, headers=headers)
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == org_data["name"]
    assert data["description"] == org_data["description"]

def test_create_organization_admin_success(admin_token):
    headers = {"Authorization": f"Bearer {admin_token}"}
    org_data = {
        "name": f"Admin Organization {uuid.uuid4().hex[:8]}",
        "description": "An admin test organization",
        # Add other required fields based on your OrganizationCreate schema
    }
    response = client.post("/organizations/", json=org_data, headers=headers)
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == org_data["name"]
    assert data["description"] == org_data["description"]

def test_create_organization_without_token():
    org_data = {
        "name": "Unauthorized Organization",
        "description": "This should fail",
    }
    response = client.post("/organizations/", json=org_data)
    assert response.status_code in [401, 403], f"Expected 401 or 403, got {response.status_code}"
    assert response.json()["detail"] == "Not authenticated"

def test_create_organization_with_invalid_token():
    headers = {"Authorization": "Bearer invalid-token"}
    org_data = {
        "name": "Invalid Token Organization",
        "description": "This should fail",
    }
    response = client.post("/organizations/", json=org_data, headers=headers)
    assert response.status_code == 401
    assert response.json()["detail"] == "Could not validate credentials"

def test_create_organization_invalid_data(user_token):
    headers = {"Authorization": f"Bearer {user_token}"}
    # Missing required fields
    org_data = {}
    response = client.post("/organizations/", json=org_data, headers=headers)
    assert response.status_code == 422  # Validation error

def test_create_organization_missing_name(user_token):
    headers = {"Authorization": f"Bearer {user_token}"}
    org_data = {
        "description": "Organization without name",
    }
    response = client.post("/organizations/", json=org_data, headers=headers)
    assert response.status_code == 422  # Validation error

# Test PUT /organizations/{org_id} - Update organization
def test_update_organization_success(user_token, test_organization):
    headers = {"Authorization": f"Bearer {user_token}"}
    update_data = {
        "name": "Updated Organization Name",
        "description": "Updated description",
        # Add other fields that can be updated based on your OrganizationUpdate schema
    }
    response = client.put(f"/organizations/{test_organization.id}", json=update_data, headers=headers)
    assert response.status_code == 200, f"Update organization failed: {response.json()}"
    data = response.json()
    assert data["name"] == update_data["name"]
    assert data["description"] == update_data["description"]

def test_update_organization_admin_success(admin_token, test_organization):
    headers = {"Authorization": f"Bearer {admin_token}"}
    update_data = {
        "name": "Admin Updated Organization",
        "description": "Admin updated description",
    }
    response = client.put(f"/organizations/{test_organization.id}", json=update_data, headers=headers)
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == update_data["name"]
    assert data["description"] == update_data["description"]

def test_update_organization_not_found(user_token):
    headers = {"Authorization": f"Bearer {user_token}"}
    update_data = {
        "name": "Updated Name",
        "description": "Updated description",
    }
    response = client.put("/organizations/99999", json=update_data, headers=headers)
    assert response.status_code == 404
    assert response.json()["detail"] == "Organization not found"

def test_update_organization_without_token(test_organization):
    update_data = {
        "name": "Updated Name",
        "description": "Updated description",
    }
    response = client.put(f"/organizations/{test_organization.id}", json=update_data)
    assert response.status_code in [401, 403]
    assert response.json()["detail"] == "Not authenticated"

def test_update_organization_with_invalid_token(test_organization):
    headers = {"Authorization": "Bearer invalid-token"}
    update_data = {
        "name": "Updated Name",
        "description": "Updated description",
    }
    response = client.put(f"/organizations/{test_organization.id}", json=update_data, headers=headers)
    assert response.status_code == 401
    assert response.json()["detail"] == "Could not validate credentials"

def test_update_organization_partial_data(user_token, test_organization):
    headers = {"Authorization": f"Bearer {user_token}"}
    update_data = {
        "name": "Only Name Updated",
        # Only updating name, not description
    }
    response = client.put(f"/organizations/{test_organization.id}", json=update_data, headers=headers)
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == update_data["name"]

# Test DELETE /organizations/{org_id} - Delete organization
def test_delete_organization_success(user_token, test_organization):
    headers = {"Authorization": f"Bearer {user_token}"}
    response = client.delete(f"/organizations/{test_organization.id}", headers=headers)
    assert response.status_code == 200
    data = response.json()
    assert data["message"] == "Organization deleted successfully"

def test_delete_organization_admin_success(admin_token, test_organization):
    headers = {"Authorization": f"Bearer {admin_token}"}
    response = client.delete(f"/organizations/{test_organization.id}", headers=headers)
    assert response.status_code == 200
    data = response.json()
    assert data["message"] == "Organization deleted successfully"

def test_delete_organization_not_found(user_token):
    headers = {"Authorization": f"Bearer {user_token}"}
    response = client.delete("/organizations/99999", headers=headers)
    assert response.status_code == 404
    assert response.json()["detail"] == "Organization not found"

def test_delete_organization_without_token(test_organization):
    response = client.delete(f"/organizations/{test_organization.id}")
    assert response.status_code in [401, 403]
    assert response.json()["detail"] == "Not authenticated"

def test_delete_organization_with_invalid_token(test_organization):
    headers = {"Authorization": "Bearer invalid-token"}
    response = client.delete(f"/organizations/{test_organization.id}", headers=headers)
    assert response.status_code == 401
    assert response.json()["detail"] == "Could not validate credentials"

# Test role-based access scenarios
def test_admin_can_access_all_organizations(admin_token):
    headers = {"Authorization": f"Bearer {admin_token}"}
    response = client.get("/organizations/", headers=headers)
    assert response.status_code == 200
    # Add more specific assertions based on your business logic

def test_user_can_access_organizations(user_token):
    headers = {"Authorization": f"Bearer {user_token}"}
    response = client.get("/organizations/", headers=headers)
    assert response.status_code == 200
    # Add more specific assertions based on your business logic

# Test edge cases
def test_get_organization_with_string_id(user_token):
    headers = {"Authorization": f"Bearer {user_token}"}
    response = client.get("/organizations/not-a-number", headers=headers)
    assert response.status_code == 422  # Validation error for invalid ID format

def test_update_organization_with_empty_data(user_token, test_organization):
    headers = {"Authorization": f"Bearer {user_token}"}
    update_data = {}
    response = client.put(f"/organizations/{test_organization.id}", json=update_data, headers=headers)
    # This might be 200 if partial updates are allowed, or 422 if some fields are required
    assert response.status_code in [200, 422]

def test_create_organization_with_duplicate_name(user_token, test_organization):
    headers = {"Authorization": f"Bearer {user_token}"}
    org_data = {
        "name": test_organization.name,  # Using same name as existing organization
        "description": "Duplicate name test",
    }
    response = client.post("/organizations/", json=org_data, headers=headers)
    # This depends on your business logic - might be allowed or not
    # Adjust the assertion based on your requirements
    assert response.status_code in [200, 400, 409]