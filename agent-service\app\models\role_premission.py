from sqlalchemy import <PERSON>umn, <PERSON><PERSON><PERSON>, String, DateT<PERSON>, BigInteger, Text, ForeignKey
from sqlalchemy.sql import func
from app.database.connection import Base

# ✅ RolePermission association table – links roles to permissions (many-to-many)
class RolePermission(Base):
    __tablename__ = "role_permissions"  # Name of the join table

    # ✅ Foreign key to the 'roles' table
    # Also serves as part of the composite primary key
    role_id = Column(BigInteger, ForeignKey("roles.id"), primary_key=True)

    # ✅ Foreign key to the 'permissions' table
    # Also part of the composite primary key
    permission_id = Column(BigInteger, ForeignKey("permissions.id"), primary_key=True)
