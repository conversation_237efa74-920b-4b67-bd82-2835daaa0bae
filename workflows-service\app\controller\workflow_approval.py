from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from fastapi import HTTPException
from app.models.workflow_approval import WorkflowApproval
from app.schemas.workflow_approval import WorkflowApprovalCreate, WorkflowApprovalUpdate
from datetime import datetime
from app.models.workflow import Workflow
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'logging-service'))
from logging_service.service_logger import create_service_logger

# Create service-specific logger
logger = create_service_logger("workflows-service")


# 🔹 Create a new workflow approval record in the database
def create_workflow_approval(db: Session, approval_data: WorkflowApprovalCreate, session_id: str = ""):
    try:
        logger.info("controller", "create_workflow_approval", f"Creating workflow approval for workflow ID: {approval_data.workflow_id}", session_id)

        # ✅ Check if the workflow exists
        workflow_exists = db.query(Workflow).filter(Workflow.id == approval_data.workflow_id).first()
        if not workflow_exists:
            logger.error("controller", "create_workflow_approval", f"Workflow with ID {approval_data.workflow_id} does not exist", session_id)
            raise HTTPException(
                status_code=400,
                detail=f" Workflow with ID {approval_data.workflow_id} does not exist"
            )

        # ✅ Create approval
        approval = WorkflowApproval(**approval_data.dict())
        db.add(approval)
        db.commit()
        db.refresh(approval)

        logger.info("controller", "create_workflow_approval", f"Successfully created workflow approval with ID: {approval.id}", session_id)
        return approval

    except SQLAlchemyError as e:
        db.rollback()
        logger.error("controller", "create_workflow_approval", f"Database error creating workflow approval: {str(e)}", session_id)
        raise HTTPException(status_code=500, detail=f" Failed to create approval: {str(e)}")

# 🔹 Fetch all workflow approval records
def get_all_approvals(db: Session, session_id: str = ""):
    try:
        logger.info("controller", "get_all_approvals", "Retrieving all workflow approvals", session_id)

        approvals = db.query(WorkflowApproval).all()

        logger.info("controller", "get_all_approvals", f"Successfully retrieved {len(approvals)} workflow approvals", session_id)
        return approvals
    except SQLAlchemyError as e:
        logger.error("controller", "get_all_approvals", f"Database error retrieving workflow approvals: {str(e)}", session_id)
        raise HTTPException(status_code=500, detail=f" Failed to fetch approvals: {str(e)}")


# 🔹 Get a single approval by its ID
def get_approval_by_id(db: Session, approval_id: int, session_id: str = ""):
    try:
        logger.info("controller", "get_approval_by_id", f"Retrieving workflow approval with ID: {approval_id}", session_id)

        approval = db.query(WorkflowApproval).filter(WorkflowApproval.id == approval_id).first()

        if approval:
            logger.info("controller", "get_approval_by_id", f"Successfully retrieved workflow approval: {approval.id}", session_id)
        else:
            logger.info("controller", "get_approval_by_id", f"Workflow approval with ID {approval_id} not found", session_id)

        return approval
    except SQLAlchemyError as e:
        logger.error("controller", "get_approval_by_id", f"Database error retrieving workflow approval {approval_id}: {str(e)}", session_id)
        raise HTTPException(status_code=500, detail=f" Failed to fetch approval by ID: {str(e)}")


# 🔹 Get all approvals tied to a specific workflow
def get_approvals_by_workflow(db: Session, workflow_id: int, session_id: str = ""):
    try:
        logger.info("controller", "get_approvals_by_workflow", f"Retrieving approvals for workflow ID: {workflow_id}", session_id)

        approvals = db.query(WorkflowApproval).filter(WorkflowApproval.workflow_id == workflow_id).all()

        logger.info("controller", "get_approvals_by_workflow", f"Successfully retrieved {len(approvals)} approvals for workflow {workflow_id}", session_id)
        return approvals
    except SQLAlchemyError as e:
        logger.error("controller", "get_approvals_by_workflow", f"Database error retrieving approvals for workflow {workflow_id}: {str(e)}", session_id)
        raise HTTPException(status_code=500, detail=f" Failed to fetch approvals for workflow: {str(e)}")


# 🔹 Update an existing approval by ID
def update_approval(db: Session, approval_id: int, update_data: WorkflowApprovalUpdate, session_id: str = ""):
    try:
        logger.info("controller", "update_approval", f"Updating workflow approval with ID: {approval_id}", session_id)

        # Find the existing record
        approval = db.query(WorkflowApproval).filter(WorkflowApproval.id == approval_id).first()
        if not approval:
            logger.info("controller", "update_approval", f"Workflow approval with ID {approval_id} not found for update", session_id)
            return None  # Caller will raise 404 if needed

        # Update only provided fields
        updated_fields = update_data.dict(exclude_unset=True)
        for key, value in updated_fields.items():
            setattr(approval, key, value)

        # If status was changed to final value, mark approved_at timestamp
        if update_data.status in ["approved", "rejected"] and not approval.approved_at:
            approval.approved_at = datetime.utcnow()

        db.commit()
        db.refresh(approval)

        logger.info("controller", "update_approval", f"Successfully updated workflow approval {approval_id}: {list(updated_fields.keys())}", session_id)
        return approval
    except SQLAlchemyError as e:
        db.rollback()
        logger.error("controller", "update_approval", f"Database error updating workflow approval {approval_id}: {str(e)}", session_id)
        raise HTTPException(status_code=500, detail=f" Failed to update approval: {str(e)}")


# 🔹 Delete an approval record
def delete_approval(db: Session, approval_id: int, session_id: str = ""):
    try:
        logger.info("controller", "delete_approval", f"Deleting workflow approval with ID: {approval_id}", session_id)

        approval = db.query(WorkflowApproval).filter(WorkflowApproval.id == approval_id).first()
        if approval:
            approval_workflow_id = approval.workflow_id  # Store for logging before deletion
            db.delete(approval)
            db.commit()

            logger.info("controller", "delete_approval", f"Successfully deleted workflow approval {approval_id} for workflow {approval_workflow_id}", session_id)
            return True
        else:
            logger.info("controller", "delete_approval", f"Workflow approval with ID {approval_id} not found for deletion", session_id)
            return False
    except SQLAlchemyError as e:
        db.rollback()
        logger.error("controller", "delete_approval", f"Database error deleting workflow approval {approval_id}: {str(e)}", session_id)
        raise HTTPException(status_code=500, detail=f" Failed to delete approval: {str(e)}")
