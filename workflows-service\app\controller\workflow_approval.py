from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from fastapi import HTTPException
from app.models.workflow_approval import WorkflowApproval
from app.schemas.workflow_approval import WorkflowApprovalCreate, WorkflowApprovalUpdate
from datetime import datetime
from app.models.workflow import Workflow


# 🔹 Create a new workflow approval record in the database
def create_workflow_approval(db: Session, approval_data: WorkflowApprovalCreate):
    try:
        # ✅ Check if the workflow exists
        workflow_exists = db.query(Workflow).filter(Workflow.id == approval_data.workflow_id).first()
        if not workflow_exists:
            raise HTTPException(
                status_code=400,
                detail=f" Workflow with ID {approval_data.workflow_id} does not exist"
            )

        # ✅ Create approval
        approval = WorkflowApproval(**approval_data.dict())
        db.add(approval)
        db.commit()
        db.refresh(approval)
        return approval

    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f" Failed to create approval: {str(e)}")

# 🔹 Fetch all workflow approval records
def get_all_approvals(db: Session):
    try:
        return db.query(WorkflowApproval).all()
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f" Failed to fetch approvals: {str(e)}")


# 🔹 Get a single approval by its ID
def get_approval_by_id(db: Session, approval_id: int):
    try:
        return db.query(WorkflowApproval).filter(WorkflowApproval.id == approval_id).first()
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f" Failed to fetch approval by ID: {str(e)}")


# 🔹 Get all approvals tied to a specific workflow
def get_approvals_by_workflow(db: Session, workflow_id: int):
    try:
        return db.query(WorkflowApproval).filter(WorkflowApproval.workflow_id == workflow_id).all()
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f" Failed to fetch approvals for workflow: {str(e)}")


# 🔹 Update an existing approval by ID
def update_approval(db: Session, approval_id: int, update_data: WorkflowApprovalUpdate):
    try:
        # Find the existing record
        approval = db.query(WorkflowApproval).filter(WorkflowApproval.id == approval_id).first()
        if not approval:
            return None  # Caller will raise 404 if needed

        # Update only provided fields
        for key, value in update_data.dict(exclude_unset=True).items():
            setattr(approval, key, value)

        # If status was changed to final value, mark approved_at timestamp
        if update_data.status in ["approved", "rejected"] and not approval.approved_at:
            approval.approved_at = datetime.utcnow()

        db.commit()
        db.refresh(approval)
        return approval
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f" Failed to update approval: {str(e)}")


# 🔹 Delete an approval record
def delete_approval(db: Session, approval_id: int):
    try:
        approval = db.query(WorkflowApproval).filter(WorkflowApproval.id == approval_id).first()
        if approval:
            db.delete(approval)
            db.commit()
        return approval
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f" Failed to delete approval: {str(e)}")
