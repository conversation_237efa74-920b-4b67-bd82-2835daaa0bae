# Use slim Python base image
FROM python:3.11-slim

# Create non-root user for better security
RUN addgroup --gid 10001 app && adduser --uid 10001 --gid 10001 --disabled-password app

# Set Python environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1

# Set working directory inside container
WORKDIR /app

# Install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy project files
COPY ./app ./app
COPY ../.env .env

# Use non-root user
USER app

# Expose FastAPI port
EXPOSE 8002

# Start FastAPI app using Uvicorn
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8002"]
