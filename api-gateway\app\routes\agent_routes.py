from fastapi import APIRouter, Depends, Request, HTTPException
import httpx
from app.dependencies.auth import verify_token
from app.core.config import AGENT_SERVICE_URL

router = APIRouter(prefix="/agents", tags=["Agents"])

# GET all agents
@router.get("/")
async def get_agents(request: Request, user_data: dict = Depends(verify_token)):
    async with httpx.AsyncClient(follow_redirects=True) as client:
        response = await client.get(
            f"{AGENT_SERVICE_URL}/agents/agents/",
            headers={
                "Authorization": request.headers.get("Authorization"),
                "accept": "application/json"
            }
        )
        if response.status_code != 200:
            raise HTTPException(status_code=response.status_code, detail=response.text)
        return response.json()

# POST create agent
@router.post("/")
async def create_agent(request: Request, data: dict, user_data: dict = Depends(verify_token)):
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{AGENT_SERVICE_URL}/agents/agents",
            json=data,
            headers={
                "Authorization": request.headers.get("Authorization"),
                "accept": "application/json"
            }
        )
        return response.json()

# GET agent by ID
@router.get("/{agent_id}")
async def get_agent_by_id(agent_id: int, request: Request, user_data: dict = Depends(verify_token)):
    async with httpx.AsyncClient() as client:
        response = await client.get(
            f"{AGENT_SERVICE_URL}/agents/agents/{agent_id}",
            headers={
                "Authorization": request.headers.get("Authorization"),
                "accept": "application/json"
            }
        )
        return response.json()

# PUT update agent
@router.put("/{agent_id}")
async def update_agent(agent_id: int, updated_data: dict, request: Request, user_data: dict = Depends(verify_token)):
    async with httpx.AsyncClient() as client:
        response = await client.put(
            f"{AGENT_SERVICE_URL}/agents/agents/{agent_id}",
            json=updated_data,
            headers={
                "Authorization": request.headers.get("Authorization"),
                "accept": "application/json"
            }
        )
        return response.json()

# DELETE agent
@router.delete("/{agent_id}")
async def delete_agent(agent_id: int, request: Request, user_data: dict = Depends(verify_token)):
    async with httpx.AsyncClient() as client:
        response = await client.delete(
            f"{AGENT_SERVICE_URL}/agents/agents/{agent_id}",
            headers={
                "Authorization": request.headers.get("Authorization"),
                "accept": "application/json"
            }
        )
        return response.json()
