from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Bearer
from jose import JW<PERSON><PERSON>r, jwt
from typing import List
from app.config import SECRET_KEY, ALGORITHM

# Make sure this matches your actual login endpoint (with prefix if using a router)
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/auth/login")

# ✅ Decode JWT and extract roles
def get_current_user_roles(token: str = Depends(oauth2_scheme)) -> List[str]:
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        roles = payload.get("roles")
        if not roles or not isinstance(roles, list):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Roles not found or invalid"
            )
        return roles
    except JW<PERSON>rror as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid token"
        )

# ✅ Require specific role(s)
def require_role(required_roles: List[str]):
    def role_checker(roles: List[str] = Depends(get_current_user_roles)):
        if not any(role in required_roles for role in roles):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access forbidden. Required roles: {required_roles}"
            )
        return True
    return role_checker