"""
Centralized Logging Service Configuration

This module provides configuration management for the centralized logging service.
It reads MongoDB connection settings and logging parameters from environment variables.
"""

import os
from dotenv import load_dotenv
from pathlib import Path

# Load environment variables
load_dotenv()

# MongoDB configuration for logging
MONGODB_URL = os.getenv("MONGODB_URL", "mongodb://localhost:27017")
MONGODB_DATABASE = os.getenv("MONGODB_DATABASE", "agent_logs")
MONGODB_COLLECTION = os.getenv("MONGODB_COLLECTION", "logs")
MONGODB_SYNC_INTERVAL = int(os.getenv("MONGODB_SYNC_INTERVAL", "10"))  # Default 10 seconds

# Logging configuration
LOG_BASE_PATH = os.getenv("LOG_BASE_PATH", "logs")  # Base path for all log files
LOG_FILE_EXTENSION = os.getenv("LOG_FILE_EXTENSION", ".log")

# Service configuration
DEFAULT_SERVICE_NAME = os.getenv("DEFAULT_SERVICE_NAME", "unknown-service")

# Debug configuration
DEBUG_LOGGING = os.getenv("DEBUG_LOGGING", "false").lower() == "true"

print(f"🔍 Centralized Logging Service Configuration:")
print(f"   MONGODB_URL: {MONGODB_URL}")
print(f"   MONGODB_DATABASE: {MONGODB_DATABASE}")
print(f"   MONGODB_COLLECTION: {MONGODB_COLLECTION}")
print(f"   MONGODB_SYNC_INTERVAL: {MONGODB_SYNC_INTERVAL}s")
print(f"   LOG_BASE_PATH: {LOG_BASE_PATH}")
print(f"   DEBUG_LOGGING: {DEBUG_LOGGING}")
