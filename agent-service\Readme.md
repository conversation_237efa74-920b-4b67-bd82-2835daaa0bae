#  Agent-Service Project

This is a FastAPI-based microservice for managing agents. It provides endpoints to **create**, **update**, **delete**, and **retrieve** agent records. The service is Dockerized and uses environment-based configuration for flexible deployment.

---

##  Features

- FastAPI framework with automatic Swagger UI
- Full CRUD operations: Create, Read, Update, Delete
- Dockerized for local and cloud deployment
- `.env` support for secure configuration

POST   /agents/         → Create agent  
GET    /agents/         → Get All agent
GET    /agents/{id}     → Get agent by ID  
PUT    /agents/{id}     → Update agent  
DELETE /agents/{id}     → Delete agent 

## 🧪 Local Development Setup



1.Create a Virtual Environment
 "python -m venv venv" 
2. Activate the Environment 
On Windows "venv\Scripts\activate"
On Mac/Linux: "source venv/bin/activate"
3.Install Required Packages
 "pip install -r requirements.txt"
4.Run the Application Locally
"uvicorn app.main:app --port 8002 --reload"
5.Open in Browser
to check the web browser "http://127.0.0.1:8002/docs"

# Docker in local 
1. Build the Docker Image
docker build -t agent-service .
2. Run the Docker Container
docker run -d -p 8002:8002 --env-file .env --name agent-container agent-service
3. Access in Browser
To docker check the web browser
http://localhost:8002

Docker OS 
OSType: linux





