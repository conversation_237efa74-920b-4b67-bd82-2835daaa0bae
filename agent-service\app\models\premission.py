from sqlalchemy import Column, Integer, String, DateTime, BigInteger, Text
from sqlalchemy.sql import func
from app.database.connection import Base

# ✅ Permission model definition – represents the 'permissions' table in the database
class Permission(Base):
    __tablename__ = "permissions"  # Database table name

    # ✅ Primary key (big integer for scalability)
    id = Column(BigInteger, primary_key=True, index=True)

    # ✅ Name of the permission (must be unique, used to identify the permission type)
    name = Column(String(100), unique=True, index=True)

    # ✅ Description of what this permission allows (e.g., "Can edit agents")
    description = Column(Text)
