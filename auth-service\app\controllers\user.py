from sqlalchemy.orm import Session
from app.models.user import User,User<PERSON><PERSON>, Role
from app.schemas.user import UserCreate,UserUpdate
from passlib.hash import bcrypt
from datetime import datetime
from sqlalchemy.orm import joinedload
from app.utils.mongo_logger import log_info, log_error

# Retrieves a user by email from the DB.
def get_user_by_email(db: Session, email: str, session_id: str = ""):
    try:
        log_info("controller", "get_user_by_email", f"Retrieving user by email: {email}", session_id)

        user = db.query(User).options(joinedload(User.roles)).filter(User.email == email).first()

        if user:
            log_info("controller", "get_user_by_email", f"User found: {user.name}", session_id)
        else:
            log_info("controller", "get_user_by_email", f"User not found for email: {email}", session_id)

        return user
    except Exception as e:
        log_error("controller", "get_user_by_email", f"Error retrieving user by email {email}: {str(e)}", session_id)
        raise

# Hashes the user's password with bcrypt.
# Creates a User record.
# Assigns a default role (via UserRole join table).
def create_user(db: Session, user: UserCreate, default_role="admin", session_id: str = ""):
    try:
        log_info("controller", "create_user", f"Creating new user: {user.email} with role: {default_role}", session_id)

        hashed_pw = bcrypt.hash(user.password) if user.password else None
        db_user = User(
            name=user.name,
            email=user.email,
            password_hash=hashed_pw,
            organization_id=user.organization_id,
            is_account_holder=False,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        db.add(db_user)
        db.commit()
        db.refresh(db_user)

        # Assign a default role by creating a UserRole join record
        role = db.query(Role).filter(Role.name == default_role).first()
        if role:
            db_user_role = UserRole(user_id=db_user.id, role_id=role.id)
            db.add(db_user_role)
            db.commit()
            log_info("controller", "create_user", f"Successfully created user {db_user.id} with role {default_role}", session_id)
        else:
            log_error("controller", "create_user", f"Role {default_role} not found", session_id)

        return db_user
    except Exception as e:
        db.rollback()
        log_error("controller", "create_user", f"Error creating user {user.email}: {str(e)}", session_id)
        raise

# Verifies a user exists and checks password hash.
def verify_user(db: Session, email: str, password: str, session_id: str = ""):
    try:
        log_info("controller", "verify_user", f"Verifying user credentials for: {email}", session_id)

        user = get_user_by_email(db, email, session_id)
        if not user:
            log_info("controller", "verify_user", f"User verification failed - user not found: {email}", session_id)
            return None

        if not bcrypt.verify(password, user.password_hash):
            log_info("controller", "verify_user", f"User verification failed - invalid password: {email}", session_id)
            return None

        log_info("controller", "verify_user", f"User verification successful: {email}", session_id)
        return user
    except Exception as e:
        log_error("controller", "verify_user", f"Error verifying user {email}: {str(e)}", session_id)
        raise

# Fetches the user's assigned role from the join between UserRole and Role.
def get_user_roles(db: Session, user_id: int, session_id: str = ""):
    try:
        log_info("controller", "get_user_roles", f"Retrieving roles for user ID: {user_id}", session_id)

        result = (
            db.query(Role.name)
            .join(UserRole, Role.id == UserRole.role_id)
            .filter(UserRole.user_id == user_id)
            .first()
        )

        role = result[0] if result else None
        log_info("controller", "get_user_roles", f"Retrieved role for user {user_id}: {role}", session_id)
        return role
    except Exception as e:
        log_error("controller", "get_user_roles", f"Error retrieving roles for user {user_id}: {str(e)}", session_id)
        raise

# ✅ Alternative method to get user role with default fallback
def get_user_role(db: Session, user_id: int) -> str:
    result = db.query(Role.name).join(UserRole).filter(UserRole.user_id == user_id).first()
    return result[0] if result else "user"  # default to 'user'

def get_all_users(db: Session):
    return db.query(User).all()

# Get single user by ID
def get_user_by_id(db: Session, user_id: int):
    return db.query(User).filter(User.id == user_id).first()

# ✅ Update fields for an existing user based on incoming data
def update_user(db: Session, user_id: int, update_data: UserUpdate):
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        return None
    for field, value in update_data.dict(exclude_unset=True).items():
        setattr(user, field, value)
    db.commit()
    db.refresh(user)
    return user