import pytest
from fastapi.testclient import TestClient
from app.main import app
from app.database.connection import SessionLocal
from app.models.user import User, Role, UserRole
from app.models.organization import Organization
from sqlalchemy.orm import Session
from app.controllers.user import create_user
from app.schemas.user import UserCreate
from app.auth.jwt_handler import create_access_token
from app.config import SECRET_KEY, ALGORITHM, ACCESS_TOKEN_EXPIRE_MINUTES
from jose import jwt
from datetime import timedelta, datetime
import google.oauth2.id_token
from unittest.mock import patch, MagicMock
from app.routes.auth_router import get_db
import uuid
import os

client = TestClient(app)

# Override get_db dependency for tests
def override_get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

# Fixtures
@pytest.fixture(scope="function")
def db_session():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@pytest.fixture(scope="function")
def test_user(db_session):
    email = f"testuser_{uuid.uuid4().hex[:8]}@example.com"
    user_data = UserCreate(
        name="Test User",
        email=email,
        password="password123",
        organization_id=1
    )
    user = create_user(db_session, user_data, default_role="user")
    db_session.commit()
    return user

@pytest.fixture(scope="function")
def admin_user(db_session):
    email = f"admin_{uuid.uuid4().hex[:8]}@example.com"
    user_data = UserCreate(
        name="Admin User",
        email=email,
        password="adminpass",
        organization_id=1
    )
    user = create_user(db_session, user_data, default_role="admin")
    db_session.commit()
    return user

@pytest.fixture(scope="function")
def user_token(test_user):
    roles = ["user"]
    access_token = create_access_token(
        data={"sub": test_user.email, "roles": roles},
        expires_delta=timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    )
    return access_token

@pytest.fixture(scope="function")
def admin_token(admin_user):
    roles = ["admin"]
    access_token = create_access_token(
        data={"sub": admin_user.email, "roles": roles},
        expires_delta=timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    )
    return access_token

@pytest.fixture(scope="function")
def expired_token(test_user):
    """Create an expired token for testing"""
    roles = ["user"]
    # Create token that expired 1 hour ago
    expired_time = datetime.utcnow() - timedelta(hours=1)
    payload = {
        "sub": test_user.email,
        "roles": roles,
        "exp": expired_time
    }
    return jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)

# Test /auth/register
def test_register_user_success(db_session):
    """Test successful user registration"""
    unique_email = f"newuser_{uuid.uuid4().hex[:8]}@example.com"
    
    response = client.post("/auth/register", json={
        "name": "New User",
        "email": unique_email,
        "password": "newpass123",
        "organization_id": 1
    })
    
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"

def test_register_user_already_exists(db_session, test_user):
    """Test registration with existing email"""
    response = client.post("/auth/register", json={
        "name": "Test User",
        "email": test_user.email,
        "password": "password123",
        "organization_id": 1
    })
    
    assert response.status_code == 400
    assert response.json()["detail"] == "Email already registered"

def test_register_user_invalid_data():
    """Test registration with invalid data"""
    response = client.post("/auth/register", json={
        "name": "",  # Empty name
        "email": "invalid-email",  # Invalid email format
        "password": "",  # Empty password
    })
    
    assert response.status_code == 422  # Validation error

# Test /auth/login
def test_login_user_success(db_session, test_user):
    """Test successful login"""
    response = client.post(
        "/auth/login",
        data={
            "username": test_user.email,
            "password": "password123"
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"

def test_login_invalid_credentials():
    """Test login with invalid credentials"""
    response = client.post(
        "/auth/login",
        data={
            "username": "<EMAIL>",
            "password": "wrongpassword"
        }
    )
    
    assert response.status_code == 400
    assert response.json()["detail"] == "Invalid credentials"

def test_login_wrong_password(test_user):
    """Test login with correct email but wrong password"""
    response = client.post(
        "/auth/login",
        data={
            "username": test_user.email,
            "password": "wrongpassword"
        }
    )
    
    assert response.status_code == 400
    assert response.json()["detail"] == "Invalid credentials"

def test_login_missing_data():
    """Test login with missing form data"""
    response = client.post("/auth/login", data={})
    
    assert response.status_code == 422  # Validation error

# Test /auth/google-login
# @patch('google.oauth2.id_token.verify_oauth2_token')
# def test_google_login_new_user_success(mock_verify, db_session):
#     """Test successful Google login for new user"""
#     mock_verify.return_value = {
#         "email": f"googleuser_{uuid.uuid4().hex[:8]}@gmail.com",
#         "name": "Google User"
#     }
    
#     response = client.post("/auth/google-login", json={
#         "token": "valid-google-token"
#     })
    
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"

# @patch('google.oauth2.id_token.verify_oauth2_token')
# def test_google_login_existing_user_success(mock_verify, db_session, test_user):
#     """Test successful Google login for existing user"""
#     mock_verify.return_value = {
#         "email": test_user.email,
#         "name": test_user.name
#     }
    
#     response = client.post("/auth/google-login", json={
#         "token": "valid-google-token"
#     })
    
#     assert response.status_code == 200
#     data = response.json()
#     assert "access_token" in data
#     assert data["token_type"] == "bearer"

# def test_google_login_missing_token():
#     """Test Google login without token"""
#     response = client.post("/auth/google-login", json={})
    
#     assert response.status_code == 400
#     assert response.json()["detail"] == "Token is required"

# @patch('google.oauth2.id_token.verify_oauth2_token')
# def test_google_login_invalid_token(mock_verify):
#     """Test Google login with invalid token"""
#     mock_verify.side_effect = ValueError("Invalid token")
    
#     response = client.post("/auth/google-login", json={
#         "token": "invalid-google-token"
#     })
    
#     assert response.status_code == 400
#     assert response.json()["detail"] == "Invalid Google token"

# @patch('google.oauth2.id_token.verify_oauth2_token')
# def test_google_login_no_email(mock_verify):
#     """Test Google login when token doesn't contain email"""
#     mock_verify.return_value = {
#         "name": "Google User"
#         # Missing email
#     }
    
#     response = client.post("/auth/google-login", json={
#         "token": "valid-google-token"
#     })
    
#     assert response.status_code == 400
#     assert response.json()["detail"] == "Invalid Google token: Email not found"

# @patch('google.oauth2.id_token.verify_oauth2_token')
# def test_google_login_general_exception(mock_verify):
#     """Test Google login with general exception"""
#     mock_verify.side_effect = Exception("Network error")
    
#     response = client.post("/auth/google-login", json={
#         "token": "valid-google-token"
#     })
    
#     assert response.status_code == 500
#     assert "Google login failed" in response.json()["detail"]

# Test /auth/refresh
def test_refresh_token_success(test_user):
    """Test successful token refresh"""
    refresh_token = create_access_token(
        data={"sub": test_user.email, "roles": ["user"]},
        expires_delta=timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    )
    
    response = client.post("/auth/refresh", json={
        "refresh_token": refresh_token
    })
    
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"

def test_refresh_token_invalid():
    """Test refresh with invalid token"""
    response = client.post("/auth/refresh", json={
        "refresh_token": "invalid-token"
    })
    
    assert response.status_code == 401
    assert response.json()["detail"] == "Invalid or expired refresh token"

def test_refresh_token_expired(expired_token):
    """Test refresh with expired token"""
    response = client.post("/auth/refresh", json={
        "refresh_token": expired_token
    })
    
    assert response.status_code == 401
    assert response.json()["detail"] == "Invalid or expired refresh token"

def test_refresh_token_no_subject():
    """Test refresh token without subject"""
    # Create token without 'sub' field
    payload = {
        "roles": ["user"],
        "exp": datetime.utcnow() + timedelta(minutes=30)
    }
    token_without_sub = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)
    
    response = client.post("/auth/refresh", json={
        "refresh_token": token_without_sub
    })
    
    assert response.status_code == 401
    assert response.json()["detail"] == "Invalid refresh token"

def test_refresh_token_missing_data():
    """Test refresh without token data"""
    response = client.post("/auth/refresh", json={})
    
    assert response.status_code == 422  # Validation error

# Test /auth/me
def test_get_current_user_success(user_token):
    """Test getting current user info"""
    headers = {"Authorization": f"Bearer {user_token}"}
    response = client.get("/auth/me", headers=headers)
    
    assert response.status_code == 200
    data = response.json()
    assert "id" in data
    assert "name" in data
    assert "email" in data
    assert "roles" in data
    assert isinstance(data["roles"], list)

def test_get_current_user_invalid_token():
    """Test getting current user with invalid token"""
    headers = {"Authorization": "Bearer invalid-token"}
    response = client.get("/auth/me", headers=headers)
    
    assert response.status_code == 401
    assert response.json()["detail"] == "Could not validate credentials"

def test_get_current_user_no_token():
    """Test getting current user without token"""
    response = client.get("/auth/me")
    
    assert response.status_code == 401

def test_get_current_user_malformed_header():
    """Test getting current user with malformed auth header"""
    headers = {"Authorization": "InvalidFormat token"}
    response = client.get("/auth/me", headers=headers)
    
    assert response.status_code == 401

def test_get_current_user_expired_token(expired_token):
    """Test getting current user with expired token"""
    headers = {"Authorization": f"Bearer {expired_token}"}
    response = client.get("/auth/me", headers=headers)
    
    assert response.status_code == 401

# Additional edge cases for better coverage
def test_admin_user_roles(admin_token):
    """Test admin user has correct roles"""
    headers = {"Authorization": f"Bearer {admin_token}"}
    response = client.get("/auth/me", headers=headers)
    
    assert response.status_code == 200
    data = response.json()
    assert "admin" in data["roles"]

def test_user_with_multiple_roles():
    """Test user with multiple roles"""
    # This would require setting up a user with multiple roles
    # Implementation depends on your user role assignment logic
    pass

# @patch.dict(os.environ, {"GOOGLE_CLIENT_ID": "test-client-id"})
# def test_google_client_id_from_env():
#     """Test Google client ID configuration from environment"""
#     from app.routes.auth_router import GOOGLE_CLIENT_ID
#     # This tests that the configuration is properly loaded
#     assert GOOGLE_CLIENT_ID is not None

# Test database connection edge cases
def test_db_session_cleanup():
    """Test database session is properly cleaned up"""
    from app.routes.auth_router import get_db
    db_gen = get_db()
    db = next(db_gen)
    assert db is not None
    # The session should be closed when the generator is exhausted
    try:
        next(db_gen)
    except StopIteration:
        pass  # Expected behavior

# Test token creation with different parameters
def test_token_creation_edge_cases(test_user):
    """Test various token creation scenarios"""
    # Test with empty roles
    token1 = create_access_token(
        data={"sub": test_user.email, "roles": []},
        expires_delta=timedelta(minutes=1)
    )
    assert token1 is not None
    
    # Test with None roles
    token2 = create_access_token(
        data={"sub": test_user.email, "roles": None},
        expires_delta=timedelta(minutes=1)
    )
    assert token2 is not None

# Performance and stress tests
def test_multiple_concurrent_requests():
    """Test handling multiple requests"""
    unique_email = f"concurrent_{uuid.uuid4().hex[:8]}@example.com"
    
    # Create multiple registration requests
    requests_data = []
    for i in range(5):
        email = f"user{i}_{uuid.uuid4().hex[:8]}@example.com"
        requests_data.append({
            "name": f"User {i}",
            "email": email,
            "password": "password123",
            "organization_id": 1
        })
    
    responses = []
    for data in requests_data:
        response = client.post("/auth/register", json=data)
        responses.append(response)
    
    # All should succeed
    for response in responses:
        assert response.status_code == 200

# Clean up fixtures and test data
@pytest.fixture(autouse=True)
def cleanup_test_data(db_session):
    """Clean up test data after each test"""
    yield
    # Additional cleanup if needed
    pass