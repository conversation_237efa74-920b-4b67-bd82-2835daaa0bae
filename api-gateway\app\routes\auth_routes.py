from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, Request, HTTPException
from fastapi.security import OAuth2PasswordRequestForm
import httpx
from app.core.config import AUTH_SERVICE_URL
from app.schema.user import UserCreate

router = APIRouter(prefix="/auth", tags=["Auth"])

@router.post("/login")
async def login(form_data: OAuth2PasswordRequestForm = Depends()):
    payload = {
        "username": form_data.username,
        "password": form_data.password
    }
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{AUTH_SERVICE_URL}/auth/login",
            data=payload
        )
        return response.json()

@router.post("/register")
async def register(user: UserCreate):
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{AUTH_SERVICE_URL}/auth/register",
            json=user.dict()
        )

        if response.status_code != 200:
            raise HTTPException(status_code=response.status_code, detail=response.text)

        return response.json()

@router.post("/google-login")
async def google_login(data: dict):
    async with httpx.AsyncClient() as client:
        response = await client.post(f"{AUTH_SERVICE_URL}/auth/google-login", json=data)
        return response.json()

@router.post("/refresh")
async def refresh_token(data: dict):
    async with httpx.AsyncClient() as client:
        response = await client.post(f"{AUTH_SERVICE_URL}/auth/refresh", json=data)
        return response.json()

@router.get("/me")
async def get_current_user(request: Request):
    async with httpx.AsyncClient() as client:
        response = await client.get(
            f"{AUTH_SERVICE_URL}/auth/me",
            headers={"Authorization": request.headers.get("Authorization")}
        )
        return response.json()
