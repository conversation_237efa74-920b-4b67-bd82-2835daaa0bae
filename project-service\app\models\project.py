from sqlalchemy import Column, String, BigInteger, Text, TIMESTAMP, ForeignKey
from datetime import datetime
from app.database.connection import Base

from sqlalchemy.orm import relationship

class Project(Base):
    __tablename__ = "projects"

    id = Column(BigInteger, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    organization_id = Column(BigInteger, ForeignKey("organizations.id"), nullable=False)
    user_id = Column(BigInteger, ForeignKey("users.id"), nullable=False)
    created_at = Column(TIMESTAMP, default=datetime.utcnow)
    updated_at = Column(TIMESTAMP, default=datetime.utcnow, onupdate=datetime.utcnow)

    organization = relationship("Organization", back_populates="projects")
    user = relationship("User", back_populates="projects")
