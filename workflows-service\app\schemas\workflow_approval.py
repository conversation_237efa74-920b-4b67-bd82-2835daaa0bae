
from pydantic import BaseModel, validator
from typing import Optional
from datetime import datetime
from enum import Enum
from app.schemas.user import ApproverInfo

class ApprovalStatus(str, Enum):
    approved = "approved"
    rejected = "rejected"


class WorkflowApprovalCreate(BaseModel):
    workflow_id: int
    approver_id: int
    status: ApprovalStatus
    comment: Optional[str] = None

    @validator("status", pre=True)
    def normalize_status(cls, v):
        if isinstance(v, str):
            return v.lower()
        return v


class WorkflowApprovalBase(BaseModel):
    workflow_id: int
    approver_id: int
    status: ApprovalStatus
    comment: Optional[str] = None


class WorkflowApprovalUpdate(BaseModel):
    status: Optional[ApprovalStatus]
    comment: Optional[str] = None
    approved_at: Optional[datetime] = None


class WorkflowApprovalOut(BaseModel):
    id: int
    workflow_id: int
    status: str
    comment: Optional[str]
    approved_at: Optional[datetime]
    approver: ApproverInfo

    class Config:
        orm_mode = True



