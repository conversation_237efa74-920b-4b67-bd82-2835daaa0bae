from sqlalchemy.orm import Session
from app.models.project import Project
from app.schemas.project import ProjectCreate, ProjectUpdate
from datetime import datetime
from sqlalchemy.orm import joinedload

def get_all(db: Session):
    return db.query(Project).options(
        joinedload(Project.organization),
        joinedload(Project.user)
    ).all()

def get_by_id(db: Session, project_id: int):
    return db.query(Project).options(
        joinedload(Project.organization),
        joinedload(Project.user)
    ).filter(Project.id == project_id).first()

def create(db: Session, data: ProjectCreate):
    new_project = Project(**data.dict())
    db.add(new_project)
    db.commit()
    db.refresh(new_project)
    return new_project

def update(db: Session, id: int, data: ProjectUpdate):
    project = get_by_id(db, id)
    if not project:
        return None
    for key, value in data.dict(exclude_unset=True).items():
        setattr(project, key, value)
    project.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(project)
    return project

def delete(db: Session, id: int):
    project = get_by_id(db, id)
    if project:
        db.delete(project)
        db.commit()
    return project