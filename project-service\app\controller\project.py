from sqlalchemy.orm import Session
from app.models.project import Project
from app.schemas.project import ProjectCreate, ProjectUpdate
from datetime import datetime
from sqlalchemy.orm import joinedload
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'logging-service'))
from logging_service.service_logger import create_service_logger

# Create service-specific logger
logger = create_service_logger("project-service")

def get_all(db: Session, session_id: str = ""):
    try:
        logger.info("controller", "get_all", "Retrieving all projects", session_id)

        projects = db.query(Project).options(
            joinedload(Project.organization),
            joinedload(Project.user)
        ).all()

        logger.info("controller", "get_all", f"Successfully retrieved {len(projects)} projects", session_id)
        return projects
    except Exception as e:
        logger.error("controller", "get_all", f"Error retrieving projects: {str(e)}", session_id)
        raise

def get_by_id(db: Session, project_id: int, session_id: str = ""):
    try:
        logger.info("controller", "get_by_id", f"Retrieving project with ID: {project_id}", session_id)

        project = db.query(Project).options(
            joinedload(Project.organization),
            joinedload(Project.user)
        ).filter(Project.id == project_id).first()

        if project:
            logger.info("controller", "get_by_id", f"Successfully retrieved project: {project.name}", session_id)
        else:
            logger.info("controller", "get_by_id", f"Project with ID {project_id} not found", session_id)

        return project
    except Exception as e:
        logger.error("controller", "get_by_id", f"Error retrieving project {project_id}: {str(e)}", session_id)
        raise

def create(db: Session, data: ProjectCreate, session_id: str = ""):
    try:
        logger.info("controller", "create", f"Creating new project: {data.name}", session_id)

        new_project = Project(**data.dict())
        db.add(new_project)
        db.commit()
        db.refresh(new_project)

        logger.info("controller", "create", f"Successfully created project with ID: {new_project.id}", session_id)
        return new_project
    except Exception as e:
        db.rollback()
        logger.error("controller", "create", f"Error creating project: {str(e)}", session_id)
        raise

def update(db: Session, id: int, data: ProjectUpdate, session_id: str = ""):
    try:
        logger.info("controller", "update", f"Updating project with ID: {id}", session_id)

        project = get_by_id(db, id, session_id)
        if not project:
            logger.info("controller", "update", f"Project with ID {id} not found for update", session_id)
            return None

        updated_fields = data.dict(exclude_unset=True)
        for key, value in updated_fields.items():
            setattr(project, key, value)
        project.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(project)

        logger.info("controller", "update", f"Successfully updated project {id}: {list(updated_fields.keys())}", session_id)
        return project
    except Exception as e:
        db.rollback()
        logger.error("controller", "update", f"Error updating project {id}: {str(e)}", session_id)
        raise

def delete(db: Session, id: int, session_id: str = ""):
    try:
        logger.info("controller", "delete", f"Deleting project with ID: {id}", session_id)

        project = get_by_id(db, id, session_id)
        if project:
            project_name = project.name  # Store name for logging before deletion
            db.delete(project)
            db.commit()

            logger.info("controller", "delete", f"Successfully deleted project {id}: {project_name}", session_id)
            return True
        else:
            logger.info("controller", "delete", f"Project with ID {id} not found for deletion", session_id)
            return False
    except Exception as e:
        db.rollback()
        logger.error("controller", "delete", f"Error deleting project {id}: {str(e)}", session_id)
        raise