from fastapi import FastAPI
from app.models import *
from app.routes.organization import router as organization
from app.database.connection import Base, engine


app = FastAPI(title="organization Service", version="1.0.0")

Base.metadata.create_all(bind=engine)
app.include_router(organization)

@app.get("/health")
def health_check():
    return {"status": "healthy", "service": "organization-service"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8004)
