import uuid
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.schemas.user import User<PERSON><PERSON>, Token, RegisterResponse, Login, RefreshTokenRequest, UserInfo
from app.models.user import User
from app.database.connection import SessionLocal
from app.controllers import user as crud_user
from app.auth.jwt_handler import create_access_token
from app.config import SECRET_KEY, ALGORITHM, ACCESS_TOKEN_EXPIRE_MINUTES
from app.utils.mongo_logger import log_info, log_error
from jose import JWTError, jwt
from datetime import timedelta
from app.dependencies.auth_dependencies import get_current_user
from decouple import config
from google.oauth2 import id_token
from google.auth.transport import requests
import os
from fastapi.security import OAuth2PasswordRequestForm

router = APIRouter(prefix="/auth", tags=["Auth"])

# Google OAuth2 configuration
GOOGLE_CLIENT_ID = config("GOOGLE_CLIENT_ID", default=os.getenv("GOOGLE_CLIENT_ID"))

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@router.post("/register", response_model=Token)
def register(user: UserCreate, db: Session = Depends(get_db)):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        log_info("router", "register", f"POST /auth/register called for email: {user.email}", session_id)

        db_user = crud_user.get_user_by_email(db, user.email, session_id)
        if db_user:
            log_error("router", "register", f"Registration failed - email already exists: {user.email}", session_id,
                     metadata={"status_code": 400})
            raise HTTPException(status_code=400, detail="Email already registered")

        # Create user with consistent role handling
        new_user = crud_user.create_user(db, user, default_role="admin", session_id=session_id)

        # Get user roles as a list
        role = crud_user.get_user_roles(db, new_user.id, session_id)
        roles = [role] if isinstance(role, str) else role  # Ensure it's a list

        token_data = {"sub": new_user.email, "roles": roles}
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        token = create_access_token(data=token_data, expires_delta=access_token_expires)

        log_info("router", "register", f"User registration successful: {user.email}", session_id,
                metadata={"status_code": 200, "user_id": new_user.id})

        return {
            "access_token": token,
            "token_type": "bearer"
        }
    except HTTPException as e:
        log_error("router", "register", f"HTTP error in register: {e.detail}", session_id,
                 metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        log_error("router", "register", f"Unexpected error in register: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/login", response_model=Token)
def login(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        log_info("router", "login", f"POST /auth/login called for user: {form_data.username}", session_id)

        db_user = crud_user.verify_user(db, form_data.username, form_data.password, session_id)
        if not db_user:
            log_error("router", "login", f"Login failed - invalid credentials: {form_data.username}", session_id,
                     metadata={"status_code": 400})
            raise HTTPException(status_code=400, detail="Invalid credentials")

        roles = [role.name for role in db_user.roles] if db_user.roles else []
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": db_user.email, "roles": roles},
            expires_delta=access_token_expires
        )

        log_info("router", "login", f"Login successful: {form_data.username}", session_id,
                metadata={"status_code": 200, "user_id": db_user.id, "roles": roles})

        return {
            "access_token": access_token,
            "token_type": "bearer"
        }
    except HTTPException as e:
        log_error("router", "login", f"HTTP error in login: {e.detail}", session_id,
                 metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        log_error("router", "login", f"Unexpected error in login: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/google-login", response_model=Token)
def google_login(request: dict, db: Session = Depends(get_db)):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        log_info("router", "google_login", "POST /auth/google-login called", session_id)

        token = request.get("token")
        if not token:
            log_error("router", "google_login", "Google login failed - token missing", session_id,
                     metadata={"status_code": 400})
            raise HTTPException(status_code=400, detail="Token is required")

        # Verify Google ID token
        idinfo = id_token.verify_oauth2_token(token, requests.Request(), GOOGLE_CLIENT_ID)

        # Extract user information
        email = idinfo.get("email")
        name = idinfo.get("name", "")

        if not email:
            log_error("router", "google_login", "Google login failed - email not found in token", session_id,
                     metadata={"status_code": 400})
            raise HTTPException(status_code=400, detail="Invalid Google token: Email not found")

        log_info("router", "google_login", f"Google login attempt for email: {email}", session_id)

        # Check if user exists, otherwise create a new one
        db_user = crud_user.get_user_by_email(db, email, session_id)
        if not db_user:
            log_info("router", "google_login", f"Creating new user from Google login: {email}", session_id)
            user_data = UserCreate(email=email, name=name, password=None)
            db_user = crud_user.create_user(db, user_data, default_role="user", session_id=session_id)

        # Get user roles
        role = crud_user.get_user_roles(db, db_user.id, session_id)
        roles = [role] if isinstance(role, str) else role  # Ensure it's a list

        # Create JWT token
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": db_user.email, "roles": roles},
            expires_delta=access_token_expires
        )

        log_info("router", "google_login", f"Google login successful: {email}", session_id,
                metadata={"status_code": 200, "user_id": db_user.id, "roles": roles})

        return {
            "access_token": access_token,
            "token_type": "bearer"
        }
    except ValueError as e:
        log_error("router", "google_login", f"Google login failed - invalid token: {str(e)}", session_id,
                 metadata={"status_code": 400})
        raise HTTPException(status_code=400, detail="Invalid Google token")
    except HTTPException as e:
        log_error("router", "google_login", f"HTTP error in google_login: {e.detail}", session_id,
                 metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        log_error("router", "google_login", f"Unexpected error in google_login: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail=f"Google login failed: {str(e)}")

@router.post("/refresh", response_model=Token)
def refresh_token(payload: RefreshTokenRequest):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        log_info("router", "refresh_token", "POST /auth/refresh called", session_id)

        payload_data = jwt.decode(payload.refresh_token, SECRET_KEY, algorithms=[ALGORITHM])
        email = payload_data.get("sub")
        roles = payload_data.get("roles", [])

        if not email:
            log_error("router", "refresh_token", "Token refresh failed - invalid token", session_id,
                     metadata={"status_code": 401})
            raise HTTPException(status_code=401, detail="Invalid refresh token")

        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        new_access_token = create_access_token(
            data={"sub": email, "roles": roles},
            expires_delta=access_token_expires
        )

        log_info("router", "refresh_token", f"Token refresh successful for: {email}", session_id,
                metadata={"status_code": 200, "roles": roles})

        return {
            "access_token": new_access_token,
            "token_type": "bearer"
        }
    except JWTError:
        log_error("router", "refresh_token", "Token refresh failed - JWT error", session_id,
                 metadata={"status_code": 401})
        raise HTTPException(status_code=401, detail="Invalid or expired refresh token")
    except Exception as e:
        log_error("router", "refresh_token", f"Unexpected error in refresh_token: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/me", response_model=UserInfo)
def read_current_user(current_user: User = Depends(get_current_user)):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        log_info("router", "read_current_user", f"GET /auth/me called for user: {current_user.email}", session_id)

        user_info = {
            "id": current_user.id,
            "name": current_user.name,
            "email": current_user.email,
            "organization_id": current_user.organization_id,
            "roles": [role.name for role in current_user.roles] if current_user.roles else []
        }

        log_info("router", "read_current_user", f"User info retrieved successfully: {current_user.email}", session_id,
                metadata={"status_code": 200, "user_id": current_user.id})

        return user_info
    except Exception as e:
        log_error("router", "read_current_user", f"Error retrieving user info: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")