from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.schemas.user import User<PERSON><PERSON>, Token, RegisterResponse, Login, RefreshTokenRequest, UserInfo
from app.models.user import User
from app.database.connection import SessionLocal
from app.controllers import user as crud_user
from app.auth.jwt_handler import create_access_token
from app.config import SECRET_KEY, ALGORITHM, ACCESS_TOKEN_EXPIRE_MINUTES
from jose import JWTError, jwt
from datetime import timedelta
from app.dependencies.auth_dependencies import get_current_user
from decouple import config
from google.oauth2 import id_token
from google.auth.transport import requests
import os
from fastapi.security import OAuth2PasswordRequestForm

router = APIRouter(prefix="/auth", tags=["Auth"])

# Google OAuth2 configuration
GOOGLE_CLIENT_ID = config("GOOGLE_CLIENT_ID", default=os.getenv("GOOGLE_CLIENT_ID"))

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@router.post("/register", response_model=Token)
def register(user: UserCreate, db: Session = Depends(get_db)):
    db_user = crud_user.get_user_by_email(db, user.email)
    if db_user:
        raise HTTPException(status_code=400, detail="Email already registered")

    # Create user with consistent role handling
    new_user = crud_user.create_user(db, user, default_role="admin")

    # Get user roles as a list
    role = crud_user.get_user_roles(db, new_user.id)
    roles = [role] if isinstance(role, str) else role  # Ensure it's a list

    token_data = {"sub": new_user.email, "roles": roles}
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    token = create_access_token(data=token_data, expires_delta=access_token_expires)

    return {
        "access_token": token,
        "token_type": "bearer"
    }

@router.post("/login", response_model=Token)
def login(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    db_user = crud_user.verify_user(db, form_data.username, form_data.password)
    if not db_user:
        raise HTTPException(status_code=400, detail="Invalid credentials")

    roles = [role.name for role in db_user.roles] if db_user.roles else []
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": db_user.email, "roles": roles},
        expires_delta=access_token_expires
    )

    return {
        "access_token": access_token,
        "token_type": "bearer"
    }

@router.post("/google-login", response_model=Token)
def google_login(request: dict, db: Session = Depends(get_db)):
    token = request.get("token")
    if not token:
        raise HTTPException(status_code=400, detail="Token is required")
    
    try:
        # Verify Google ID token
        idinfo = id_token.verify_oauth2_token(token, requests.Request(), GOOGLE_CLIENT_ID)

        # Extract user information
        email = idinfo.get("email")
        name = idinfo.get("name", "")

        if not email:
            raise HTTPException(status_code=400, detail="Invalid Google token: Email not found")

        # Check if user exists, otherwise create a new one
        db_user = crud_user.get_user_by_email(db, email)
        if not db_user:
            user_data = UserCreate(email=email, name=name, password=None)
            db_user = crud_user.create_user(db, user_data, default_role="user")

        # Get user roles
        role = crud_user.get_user_roles(db, db_user.id)
        roles = [role] if isinstance(role, str) else role  # Ensure it's a list

        # Create JWT token
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": db_user.email, "roles": roles},
            expires_delta=access_token_expires
        )

        return {
            "access_token": access_token,
            "token_type": "bearer"
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail="Invalid Google token")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Google login failed: {str(e)}")

@router.post("/refresh", response_model=Token)
def refresh_token(payload: RefreshTokenRequest):
    try:
        payload_data = jwt.decode(payload.refresh_token, SECRET_KEY, algorithms=[ALGORITHM])
        email = payload_data.get("sub")
        roles = payload_data.get("roles", [])

        if not email:
            raise HTTPException(status_code=401, detail="Invalid refresh token")

        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        new_access_token = create_access_token(
            data={"sub": email, "roles": roles},
            expires_delta=access_token_expires
        )
        return {
            "access_token": new_access_token,
            "token_type": "bearer"
        }
    except JWTError:
        raise HTTPException(status_code=401, detail="Invalid or expired refresh token")

@router.get("/me", response_model=UserInfo)
def read_current_user(current_user: User = Depends(get_current_user)):
    return {
        "id": current_user.id,
        "name": current_user.name,
        "email": current_user.email,
        "organization_id": current_user.organization_id,
        "roles": [role.name for role in current_user.roles] if current_user.roles else []
    }