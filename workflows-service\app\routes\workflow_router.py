import uuid
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.database.connection import get_db
from app.schemas.workflow import WorkflowCreate, WorkflowUpdate, WorkflowOut
from app.controller import workflows
from app.controller import user as crud_user
from app.dependencies.auth_dependencies import get_current_user
from app.utils.permission import user_has_permission
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'logging-service'))
from logging_service.service_logger import create_service_logger

# Create service-specific logger
logger = create_service_logger("workflows-service")




router = APIRouter(prefix="/workflows", tags=["Workflows"])

@router.post("/", response_model=WorkflowOut)
def create(workflow: WorkflowCreate, db: Session = Depends(get_db),current_user = Depends(get_current_user)):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        logger.info("router", "create", f"POST /workflows/ called by user {current_user.id}", session_id)

        if not user_has_permission(current_user.id, "create_workflow", db):
            logger.error("router", "create", f"Access denied for user {current_user.id}", session_id,
                     metadata={"status_code": 403})
            raise HTTPException(status_code=403, detail="Access denied")

        result = workflows.create_workflow(db, workflow, session_id)
        logger.info("router", "create", f"Successfully created workflow via API", session_id,
                metadata={"status_code": 200, "workflow_id": result.id})
        return result

    except HTTPException as e:
        logger.error("router", "create", f"HTTP error in create: {e.detail}", session_id,
                 metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        logger.error("router", "create", f"Unexpected error in create: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/", response_model=list[WorkflowOut])
def list_all(db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        logger.info("router", "list_all", f"GET /workflows/ called by user {current_user.id}", session_id)

        if not user_has_permission(current_user.id, "read_workflow", db):
            logger.error("router", "list_all", f"Access denied for user {current_user.id}", session_id,
                     metadata={"status_code": 403})
            raise HTTPException(status_code=403, detail="Access denied")

        workflows_list = workflows.get_all_workflows(db, session_id)  # ✅ fixed
        logger.info("router", "list_all", f"Successfully retrieved workflows via API", session_id,
                metadata={"status_code": 200, "workflow_count": len(workflows_list)})
        return workflows_list

    except HTTPException as e:
        logger.error("router", "list_all", f"HTTP error in list_all: {e.detail}", session_id,
                 metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        logger.error("router", "list_all", f"Unexpected error in list_all: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/{workflow_id}", response_model=WorkflowOut)
def get_by_id(workflow_id: int, db: Session = Depends(get_db),current_user = Depends(get_current_user)):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        logger.info("router", "get_by_id", f"GET /workflows/{workflow_id} called by user {current_user.id}", session_id)

        if not user_has_permission(current_user.id, "read_workflow", db):
            logger.error("router", "get_by_id", f"Access denied for user {current_user.id}", session_id,
                     metadata={"status_code": 403})
            raise HTTPException(status_code=403, detail="Access denied")

        wf = workflows.get_workflow_by_id(db, workflow_id, session_id)
        if not wf:
            logger.error("router", "get_by_id", f"Workflow {workflow_id} not found", session_id,
                     metadata={"status_code": 404})
            raise HTTPException(status_code=404, detail="Workflow not found")

        logger.info("router", "get_by_id", f"Successfully retrieved workflow {workflow_id} via API", session_id,
                metadata={"status_code": 200, "workflow_name": wf.name})
        return wf

    except HTTPException as e:
        logger.error("router", "get_by_id", f"HTTP error in get_by_id: {e.detail}", session_id,
                 metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        logger.error("router", "get_by_id", f"Unexpected error in get_by_id: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")

@router.put("/{workflow_id}", response_model=WorkflowOut)
def update(workflow_id: int, data: WorkflowUpdate, db: Session = Depends(get_db),current_user = Depends(get_current_user)):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        logger.info("router", "update", f"PUT /workflows/{workflow_id} called by user {current_user.id}", session_id)

        if not user_has_permission(current_user.id, "update_workflow", db):
            logger.error("router", "update", f"Access denied for user {current_user.id}", session_id,
                     metadata={"status_code": 403})
            raise HTTPException(status_code=403, detail="Access denied")

        updated = workflows.update_workflow(db, workflow_id, data, session_id)
        if not updated:
            logger.error("router", "update", f"Workflow {workflow_id} not found for update", session_id,
                     metadata={"status_code": 404})
            raise HTTPException(status_code=404, detail="Workflow not found")

        logger.info("router", "update", f"Successfully updated workflow {workflow_id} via API", session_id,
                metadata={"status_code": 200, "workflow_name": updated.name})
        return updated

    except HTTPException as e:
        logger.error("router", "update", f"HTTP error in update: {e.detail}", session_id,
                 metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        logger.error("router", "update", f"Unexpected error in update: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")

@router.delete("/{workflow_id}")
def delete(workflow_id: int, db: Session = Depends(get_db),current_user = Depends(get_current_user)):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        logger.info("router", "delete", f"DELETE /workflows/{workflow_id} called by user {current_user.id}", session_id)

        if not user_has_permission(current_user.id, "delete_workflow", db):
            logger.error("router", "delete", f"Access denied for user {current_user.id}", session_id,
                     metadata={"status_code": 403})
            raise HTTPException(status_code=403, detail="Access denied")

        success = workflows.delete_workflow(db, workflow_id, session_id)
        if not success:
            logger.error("router", "delete", f"Workflow {workflow_id} not found for deletion", session_id,
                     metadata={"status_code": 404})
            raise HTTPException(status_code=404, detail="Workflow not found")

        logger.info("router", "delete", f"Successfully deleted workflow {workflow_id} via API", session_id,
                metadata={"status_code": 200})
        return {"detail": "Workflow deleted successfully"}

    except HTTPException as e:
        logger.error("router", "delete", f"HTTP error in delete: {e.detail}", session_id,
                 metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        logger.error("router", "delete", f"Unexpected error in delete: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")