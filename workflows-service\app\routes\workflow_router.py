from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.database.connection import get_db
from app.schemas.workflow import WorkflowCreate, WorkflowUpdate, WorkflowOut
from app.controller import workflows
from app.controller import user as crud_user
from app.dependencies.auth_dependencies import get_current_user
from app.utils.permission import user_has_permission




router = APIRouter(prefix="/workflows", tags=["Workflows"])

@router.post("/", response_model=WorkflowOut)
def create(workflow: WorkflowCreate, db: Session = Depends(get_db),current_user = Depends(get_current_user)):
    if not user_has_permission(current_user.id, "create_workflow", db):
        raise HTTPException(status_code=403, detail="Access denied")
    return workflows.create_workflow(db, workflow)

@router.get("/", response_model=list[WorkflowOut])
def list_all(db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    if not user_has_permission(current_user.id, "read_workflow", db):
        raise HTTPException(status_code=403, detail="Access denied")

    workflows_list = workflows.get_all_workflows(db)  # ✅ fixed
    return workflows_list

@router.get("/{workflow_id}", response_model=WorkflowOut)
def get_by_id(workflow_id: int, db: Session = Depends(get_db),current_user = Depends(get_current_user)):
        if not user_has_permission(current_user.id, "read_workflow", db):
            raise HTTPException(status_code=403, detail="Access denied")
        wf = workflows.get_workflow_by_id(db, workflow_id)
        if not wf:
            raise HTTPException(status_code=404, detail="Workflow not found")
        return wf

@router.put("/{workflow_id}", response_model=WorkflowOut)
def update(workflow_id: int, data: WorkflowUpdate, db: Session = Depends(get_db),current_user = Depends(get_current_user)):
    if not user_has_permission(current_user.id, "update_workflow", db):
        raise HTTPException(status_code=403, detail="Access denied")
    updated = workflows.update_workflow(db, workflow_id, data)
    if not updated:
        raise HTTPException(status_code=404, detail="Workflow not found")
    return updated

@router.delete("/{workflow_id}")
def delete(workflow_id: int, db: Session = Depends(get_db),current_user = Depends(get_current_user)):
    if not user_has_permission(current_user.id, "delete_workflow", db):
        raise HTTPException(status_code=403, detail="Access denied")
    success = workflows.delete_workflow(db, workflow_id)
    if not success:
        raise HTTPException(status_code=404, detail="Workflow not found")
    return {"detail": "Workflow deleted successfully"}