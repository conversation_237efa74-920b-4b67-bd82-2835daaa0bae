from sqlalchemy.orm import Session, joinedload
from sqlalchemy.exc import SQLAlchemyError
from fastapi import HTTPException
from app.models.user import User, UserRole, Role
from app.schemas.user import UserCreate, UserUpdate
from passlib.hash import bcrypt
from datetime import datetime

# ✅ Retrieve a user by their email, including their roles (eager loading)
def get_user_by_email(db: Session, email: str):
    try:
        return db.query(User).options(joinedload(User.roles)).filter(User.email == email).first()
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=str(e))

# ✅ Create a new user and assign a default role (default is "admin")
def create_user(db: Session, user: UserCreate, default_role="admin"):
    try:
        # Hash the user's password using bcrypt
        hashed_pw = bcrypt.hash(user.password)

        # Create a new user instance with timestamps
        db_user = User(
            name=user.name,
            email=user.email,
            password_hash=hashed_pw,
            organization_id=user.organization_id,
            is_account_holder=False,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )

        # Add the new user to the database
        db.add(db_user)
        db.commit()
        db.refresh(db_user)

        # 🔒 Assign the default role (e.g., admin) to the new user
        role = db.query(Role).filter(Role.name == default_role).first()
        if role:
            db_user_role = UserRole(user_id=db_user.id, role_id=role.id)
            db.add(db_user_role)
            db.commit()

        return db_user
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

# ✅ Verify user credentials by comparing email and password
def verify_user(db: Session, email: str, password: str):
    try:
        user = get_user_by_email(db, email)
        # Return None if user not found or password doesn't match
        if not user or not bcrypt.verify(password, user.password_hash):
            return None
        return user
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=str(e))

# ✅ Get the name of the first role assigned to a user
def get_user_roles(db: Session, user_id: int):
    try:
        result = (
            db.query(Role.name)
            .join(UserRole, Role.id == UserRole.role_id)
            .filter(UserRole.user_id == user_id)
            .first()
        )
        return result[0] if result else None
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=str(e))

# ✅ Fallback version of get_user_roles that returns "user" if no role found
def get_user_role(db: Session, user_id: int) -> str:
    try:
        result = db.query(Role.name).join(UserRole).filter(UserRole.user_id == user_id).first()
        return result[0] if result else "user"
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=str(e))

# ✅ Get a list of all users in the database
def get_all_users(db: Session):
    try:
        return db.query(User).all()
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=str(e))

# ✅ Retrieve a user by their unique ID
def get_user_by_id(db: Session, user_id: int):
    try:
        return db.query(User).filter(User.id == user_id).first()
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=str(e))

# ✅ Update an existing user’s information using provided fields
def update_user(db: Session, user_id: int, update_data: UserUpdate):
    try:
        # Find user by ID
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            return None  # User doesn't exist

        # Only update fields that are provided (exclude unset fields)
        for field, value in update_data.dict(exclude_unset=True).items():
            setattr(user, field, value)

        db.commit()
        db.refresh(user)  # Refresh with latest DB state
        return user
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))
