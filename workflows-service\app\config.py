import os
from dotenv import load_dotenv
from pathlib import Path

print("📂 Current working directory:", Path().absolute())
print("📄 .env file found:", Path(".env").exists())

env_path = Path(__file__).resolve().parent.parent / ".env"
load_dotenv(dotenv_path=env_path)


SECRET_KEY = os.getenv("SECRET_KEY")
ALGORITHM = os.getenv("ALGORITHM")
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES"))
DATABASE_URL = os.getenv("DATABASE_URL")
print("🔍 DATABASE_URL from .env:", DATABASE_URL)