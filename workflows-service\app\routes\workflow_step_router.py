from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.schemas.workflow_step import WorkflowStepCreate, WorkflowStepOut, WorkflowStepUpdate
from app.controller import workflow_step as step_crud
from app.dependencies.auth_dependencies import get_db
from app.dependencies.auth_dependencies import get_current_user
from app.utils.permission import user_has_permission

router = APIRouter(prefix="/workflow-steps", tags=["Workflow Steps"])

@router.post("/", response_model=WorkflowStepOut)
def create_step(step: WorkflowStepCreate, db: Session = Depends(get_db),current_user = Depends(get_current_user)):
    if not user_has_permission(current_user.id, "create_workflow", db):
        raise HTTPException(status_code=403, detail="Access denied")
    return step_crud.create_step(db, step)

@router.get("/", response_model=list[WorkflowStepOut])
def list_steps(db: Session = Depends(get_db),current_user = Depends(get_current_user)):
    if not user_has_permission(current_user.id, "read_workflow", db):
        raise HTTPException(status_code=403, detail="Access denied")
    return step_crud.get_all_steps(db)

@router.get("/{step_id}", response_model=WorkflowStepOut)
def get_step(step_id: int, db: Session = Depends(get_db),current_user = Depends(get_current_user)):
    if not user_has_permission(current_user.id, "read_workflow", db):
        raise HTTPException(status_code=403, detail="Access denied")
    step = step_crud.get_step(db, step_id)
    if not step:
        raise HTTPException(status_code=404, detail="Step not found")
    return step

@router.put("/{step_id}", response_model=WorkflowStepOut)
def update_step(step_id: int, data: WorkflowStepUpdate, db: Session = Depends(get_db),current_user = Depends(get_current_user)):
    if not user_has_permission(current_user.id, "update_workflow", db):
        raise HTTPException(status_code=403, detail="Access denied")
    updated = step_crud.update_step(db, step_id, data)
    if not updated:
        raise HTTPException(status_code=404, detail="Step not found")
    return updated

@router.delete("/{step_id}")
def delete_step(step_id: int, db: Session = Depends(get_db),current_user = Depends(get_current_user)):
    if not user_has_permission(current_user.id, "delete_workflow", db):
        raise HTTPException(status_code=403, detail="Access denied")
    deleted = step_crud.delete_step(db, step_id)
    if not deleted:
        raise HTTPException(status_code=404, detail="Step not found")
    return {"message": "Deleted successfully"}
