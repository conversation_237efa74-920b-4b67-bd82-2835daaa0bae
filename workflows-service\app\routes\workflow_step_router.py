import uuid
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.schemas.workflow_step import WorkflowStepCreate, WorkflowStepOut, WorkflowStepUpdate
from app.controller import workflow_step as step_crud
from app.dependencies.auth_dependencies import get_db
from app.dependencies.auth_dependencies import get_current_user
from app.utils.permission import user_has_permission
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'logging-service'))
from logging_service.service_logger import create_service_logger

# Create service-specific logger
logger = create_service_logger("workflows-service")

router = APIRouter(prefix="/workflow-steps", tags=["Workflow Steps"])

@router.post("/", response_model=WorkflowStepOut)
def create_step(step: WorkflowStepCreate, db: Session = Depends(get_db),current_user = Depends(get_current_user)):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        logger.info("router", "create_step", f"POST /workflow-steps/ called by user {current_user.id}", session_id)

        if not user_has_permission(current_user.id, "create_workflow", db):
            logger.error("router", "create_step", f"Access denied for user {current_user.id}", session_id,
                     metadata={"status_code": 403})
            raise HTTPException(status_code=403, detail="Access denied")

        result = step_crud.create_step(db, step, session_id)
        logger.info("router", "create_step", f"Successfully created workflow step via API", session_id,
                metadata={"status_code": 200, "step_id": result.id})
        return result

    except HTTPException as e:
        logger.error("router", "create_step", f"HTTP error in create_step: {e.detail}", session_id,
                 metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        logger.error("router", "create_step", f"Unexpected error in create_step: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/", response_model=list[WorkflowStepOut])
def list_steps(db: Session = Depends(get_db),current_user = Depends(get_current_user)):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        logger.info("router", "list_steps", f"GET /workflow-steps/ called by user {current_user.id}", session_id)

        if not user_has_permission(current_user.id, "read_workflow", db):
            logger.error("router", "list_steps", f"Access denied for user {current_user.id}", session_id,
                     metadata={"status_code": 403})
            raise HTTPException(status_code=403, detail="Access denied")

        result = step_crud.get_all_steps(db, session_id)
        logger.info("router", "list_steps", f"Successfully retrieved workflow steps via API", session_id,
                metadata={"status_code": 200, "step_count": len(result)})
        return result

    except HTTPException as e:
        logger.error("router", "list_steps", f"HTTP error in list_steps: {e.detail}", session_id,
                 metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        logger.error("router", "list_steps", f"Unexpected error in list_steps: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/{step_id}", response_model=WorkflowStepOut)
def get_step(step_id: int, db: Session = Depends(get_db), current_user = Depends(get_current_user)):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        logger.info("router", "get_step", f"GET /workflow-steps/{step_id} called by user {current_user.id}", session_id)

        if not user_has_permission(current_user.id, "read_workflow", db):
            logger.error("router", "get_step", f"Access denied for user {current_user.id}", session_id,
                        metadata={"status_code": 403})
            raise HTTPException(status_code=403, detail="Access denied")

        step = step_crud.get_step(db, step_id, session_id)
        if not step:
            logger.error("router", "get_step", f"Workflow step {step_id} not found", session_id,
                        metadata={"status_code": 404})
            raise HTTPException(status_code=404, detail="Step not found")

        logger.info("router", "get_step", f"Successfully retrieved workflow step {step_id}", session_id,
                   metadata={"status_code": 200, "step_name": step.name})
        return step

    except HTTPException as e:
        logger.error("router", "get_step", f"HTTP error in get_step: {e.detail}", session_id,
                    metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        logger.error("router", "get_step", f"Unexpected error in get_step: {str(e)}", session_id,
                    metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")

@router.put("/{step_id}", response_model=WorkflowStepOut)
def update_step(step_id: int, data: WorkflowStepUpdate, db: Session = Depends(get_db), current_user = Depends(get_current_user)):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        logger.info("router", "update_step", f"PUT /workflow-steps/{step_id} called by user {current_user.id}", session_id)

        if not user_has_permission(current_user.id, "update_workflow", db):
            logger.error("router", "update_step", f"Access denied for user {current_user.id}", session_id,
                        metadata={"status_code": 403})
            raise HTTPException(status_code=403, detail="Access denied")

        updated = step_crud.update_step(db, step_id, data, session_id)
        if not updated:
            logger.error("router", "update_step", f"Workflow step {step_id} not found for update", session_id,
                        metadata={"status_code": 404})
            raise HTTPException(status_code=404, detail="Step not found")

        logger.info("router", "update_step", f"Successfully updated workflow step {step_id}", session_id,
                   metadata={"status_code": 200, "step_name": updated.name})
        return updated

    except HTTPException as e:
        logger.error("router", "update_step", f"HTTP error in update_step: {e.detail}", session_id,
                    metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        logger.error("router", "update_step", f"Unexpected error in update_step: {str(e)}", session_id,
                    metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")

@router.delete("/{step_id}")
def delete_step(step_id: int, db: Session = Depends(get_db), current_user = Depends(get_current_user)):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        logger.info("router", "delete_step", f"DELETE /workflow-steps/{step_id} called by user {current_user.id}", session_id)

        if not user_has_permission(current_user.id, "delete_workflow", db):
            logger.error("router", "delete_step", f"Access denied for user {current_user.id}", session_id,
                        metadata={"status_code": 403})
            raise HTTPException(status_code=403, detail="Access denied")

        deleted = step_crud.delete_step(db, step_id, session_id)
        if not deleted:
            logger.error("router", "delete_step", f"Workflow step {step_id} not found for deletion", session_id,
                        metadata={"status_code": 404})
            raise HTTPException(status_code=404, detail="Step not found")

        logger.info("router", "delete_step", f"Successfully deleted workflow step {step_id}", session_id,
                   metadata={"status_code": 200})
        return {"message": "Deleted successfully"}

    except HTTPException as e:
        logger.error("router", "delete_step", f"HTTP error in delete_step: {e.detail}", session_id,
                    metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        logger.error("router", "delete_step", f"Unexpected error in delete_step: {str(e)}", session_id,
                    metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")
