from pydantic import BaseModel
from typing import Optional
from datetime import datetime


class AssignedUserOut(BaseModel):
    id: int
    email: str
    name: str

    class Config:
        orm_mode = True

class WorkflowStepOut(BaseModel):
    id: int
    workflow_id: int
    step_number: int
    name: str
    description: Optional[str]
    assigned_user: Optional[AssignedUserOut]
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class WorkflowStepBase(BaseModel):
    workflow_id: int
    step_number: int
    name: str
    description: Optional[str] = None
    assigned_to: Optional[int] = None

class WorkflowStepCreate(WorkflowStepBase):
    pass

class WorkflowStepUpdate(BaseModel):
    step_number: Optional[int] = None
    name: Optional[str] = None
    description: Optional[str] = None
    assigned_to: Optional[int] = None

