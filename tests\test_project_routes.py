import pytest
from fastapi.testclient import TestClient
from app.main import app
from app.database.connection import SessionLocal
from app.models.user import User, Role, UserRole
from app.models.organization import Organization
from app.models.project import Project  # Assuming you have a Project model
from sqlalchemy.orm import Session
from app.controllers.user import create_user
from app.controllers.project import create as create_project_controller
from app.schemas.user import UserCreate
from app.schemas.project import ProjectCreate
from app.auth.jwt_handler import create_access_token
from app.database.connection import get_db  # Import get_db from project router
import uuid

client = TestClient(app)

# Override get_db dependency for tests
def override_get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Fix the dependency override
app.dependency_overrides[get_db] = override_get_db

# Fixtures
@pytest.fixture(scope="function")
def db_session():
    db = SessionLocal()
    try:
        # Start a savepoint transaction for better isolation
        db.begin()
        yield db
        db.rollback()  # Rollback after each test
    finally:
        db.close()

@pytest.fixture(scope="function")
def test_user(db_session):
    # Use a unique email for each test run
    email = f"testuser_{uuid.uuid4().hex[:8]}@example.com"
    
    user_data = UserCreate(
        name="Test User",
        email=email,
        password="password123",
        organization_id=1
    )
    user = create_user(db_session, user_data, default_role="user")
    db_session.commit()
    return user

@pytest.fixture(scope="function")
def admin_user(db_session):
    # Use a unique email for each test run
    email = f"admin_{uuid.uuid4().hex[:8]}@example.com"
    
    user_data = UserCreate(
        name="Admin User",
        email=email,
        password="adminpass",
        organization_id=1
    )
    user = create_user(db_session, user_data, default_role="admin")
    db_session.commit()
    return user

@pytest.fixture(scope="function")
def user_token(test_user):
    roles = ["user"]
    access_token = create_access_token(data={"sub": test_user.email, "roles": roles})
    return access_token

@pytest.fixture(scope="function")
def admin_token(admin_user):
    roles = ["admin"]
    access_token = create_access_token(data={"sub": admin_user.email, "roles": roles})
    return access_token

@pytest.fixture(scope="function")
def test_project(db_session, test_user):  # Get user from fixture
    project_data = ProjectCreate(
        name="Test Project",
        description="This is a test project",
        organization_id=test_user.organization_id,  # ✅ use test user org
        user_id=test_user.id                        # ✅ use test user ID
    )
    project = create_project_controller(db_session, project_data)
    db_session.commit()
    return project


# Test GET /projects/ - Get all projects
def test_get_all_projects_with_user_token(user_token):
    headers = {"Authorization": f"Bearer {user_token}"}
    response = client.get("/projects/", headers=headers)
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)

def test_get_all_projects_with_admin_token(admin_token):
    headers = {"Authorization": f"Bearer {admin_token}"}
    response = client.get("/projects/", headers=headers)
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)

def test_get_all_projects_without_token():
    response = client.get("/projects/")
    assert response.status_code in [401, 403]
    assert response.json()["detail"] == "Not authenticated"

def test_get_all_projects_with_invalid_token():
    headers = {"Authorization": "Bearer invalid-token"}
    response = client.get("/projects/", headers=headers)
    assert response.status_code == 401
    assert response.json()["detail"] == "Could not validate credentials"

# Test GET /projects/{project_id} - Get project by ID
def test_get_project_by_id_success(user_token, test_project):
    headers = {"Authorization": f"Bearer {user_token}"}
    response = client.get(f"/projects/{test_project.id}", headers=headers)
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_project.id
    assert data["name"] == test_project.name

def test_get_project_by_id_not_found(user_token):
    headers = {"Authorization": f"Bearer {user_token}"}
    response = client.get("/projects/99999", headers=headers)
    assert response.status_code == 404
    assert response.json()["detail"] == "Project not found"

def test_get_project_by_id_without_token(test_project):
    response = client.get(f"/projects/{test_project.id}")
    assert response.status_code in [401, 403]
    assert response.json()["detail"] == "Not authenticated"

def test_get_project_by_id_with_invalid_token(test_project):
    headers = {"Authorization": "Bearer invalid-token"}
    response = client.get(f"/projects/{test_project.id}", headers=headers)
    assert response.status_code == 401
    assert response.json()["detail"] == "Could not validate credentials"

# Test POST /projects/ - Create project
def test_create_project_success(user_token, db_session, test_user):
    headers = {"Authorization": f"Bearer {user_token}"}
    project_data = {
        "name": f"New Project {uuid.uuid4().hex[:8]}",
        "description": "A new test project",
        "organization_id": test_user.organization_id,  # ✅ Required
        "user_id": test_user.id                        # ✅ Required
    }
    response = client.post("/projects/", json=project_data, headers=headers)
    assert response.status_code == 200


def test_create_project_admin_success(admin_token, db_session, admin_user):
    headers = {"Authorization": f"Bearer {admin_token}"}
    project_data = {
        "name": f"Admin Project {uuid.uuid4().hex[:8]}",
        "description": "An admin test project",
        "organization_id": admin_user.organization_id,
        "user_id": admin_user.id
    }
    response = client.post("/projects/", json=project_data, headers=headers)
    assert response.status_code == 200

def test_create_project_without_token():
    project_data = {
        "name": "Unauthorized Project",
        "description": "This should fail",
    }
    response = client.post("/projects/", json=project_data)
    assert response.status_code in [401, 403], f"Expected 401 or 403, got {response.status_code}"

def test_create_project_with_invalid_token():
    headers = {"Authorization": "Bearer invalid-token"}
    project_data = {
        "name": "Invalid Token Project",
        "description": "This should fail",
    }
    response = client.post("/projects/", json=project_data, headers=headers)
    assert response.status_code == 401
    assert response.json()["detail"] == "Could not validate credentials"

def test_create_project_invalid_data(user_token):
    headers = {"Authorization": f"Bearer {user_token}"}
    # Missing required fields
    project_data = {}
    response = client.post("/projects/", json=project_data, headers=headers)
    assert response.status_code == 422  # Validation error

# Test PUT /projects/{project_id} - Update project
def test_update_project_success(user_token, test_project):
    headers = {"Authorization": f"Bearer {user_token}"}
    update_data = {
        "name": "Updated Project Name",
        "description": "Updated description",
        # Add other fields that can be updated
    }
    response = client.put(f"/projects/{test_project.id}", json=update_data, headers=headers)
    assert response.status_code == 200, f"Update project failed: {response.json()}"
    data = response.json()
    assert data["name"] == update_data["name"]
    assert data["description"] == update_data["description"]

def test_update_project_not_found(user_token):
    headers = {"Authorization": f"Bearer {user_token}"}
    update_data = {
        "name": "Updated Name",
        "description": "Updated description",
    }
    response = client.put("/projects/99999", json=update_data, headers=headers)
    assert response.status_code == 404
    assert response.json()["detail"] == "Project not found"

def test_update_project_without_token(test_project):
    update_data = {
        "name": "Updated Name",
        "description": "Updated description",
    }
    response = client.put(f"/projects/{test_project.id}", json=update_data)
    assert response.status_code in [401, 403]
    assert response.json()["detail"] == "Not authenticated"

def test_update_project_with_invalid_token(test_project):
    headers = {"Authorization": "Bearer invalid-token"}
    update_data = {
        "name": "Updated Name",
        "description": "Updated description",
    }
    response = client.put(f"/projects/{test_project.id}", json=update_data, headers=headers)
    assert response.status_code == 401
    assert response.json()["detail"] == "Could not validate credentials"

# Test DELETE /projects/{project_id} - Delete project
def test_delete_project_success(user_token, test_project):
    headers = {"Authorization": f"Bearer {user_token}"}
    response = client.delete(f"/projects/{test_project.id}", headers=headers)
    assert response.status_code == 200
    data = response.json()
    assert data["message"] == "Project deleted successfully"

def test_delete_project_not_found(user_token):
    headers = {"Authorization": f"Bearer {user_token}"}
    response = client.delete("/projects/99999", headers=headers)
    assert response.status_code == 404
    assert response.json()["detail"] == "Project not found"

def test_delete_project_without_token(test_project):
    response = client.delete(f"/projects/{test_project.id}")
    assert response.status_code in [401, 403]
    assert response.json()["detail"] == "Not authenticated"

def test_delete_project_with_invalid_token(test_project):
    headers = {"Authorization": "Bearer invalid-token"}
    response = client.delete(f"/projects/{test_project.id}", headers=headers)
    assert response.status_code == 401
    assert response.json()["detail"] == "Could not validate credentials"

# Test admin-specific scenarios (if there are role-based restrictions)
def test_admin_can_access_all_projects(admin_token):
    headers = {"Authorization": f"Bearer {admin_token}"}
    response = client.get("/projects/", headers=headers)
    assert response.status_code == 200
    # Add more specific assertions based on your business logic

def test_user_can_access_projects(user_token):
    headers = {"Authorization": f"Bearer {user_token}"}
    response = client.get("/projects/", headers=headers)
    assert response.status_code == 200
    # Add more specific assertions based on your business logic