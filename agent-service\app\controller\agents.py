from sqlalchemy.orm import Session, joinedload
from sqlalchemy.exc import SQLAlchemyError
from fastapi import HTTPException
from app.models.agent import Agent
from app.schemas.agents import AgentCreate, AgentUpdate
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'logging-service'))
from logging_service.service_logger import create_service_logger

# Create service-specific logger
logger = create_service_logger("agent-service")

# ✅ Function to create a new agent entry in the database
def create_agent(db: Session, agent: AgentCreate, session_id: str = ""):
    try:
        logger.info("controller", "create_agent", f"Creating new agent: {agent.name}", session_id)

        # Convert Pydantic schema to ORM model instance
        db_agent = Agent(**agent.dict())
        db.add(db_agent)  # Add agent to session
        db.commit()       # Commit transaction to persist in DB
        db.refresh(db_agent)  # Refresh instance with DB-generated values (e.g., ID)

        logger.info("controller", "create_agent", f"Successfully created agent with ID: {db_agent.id}", session_id)
        return db_agent
    except SQLAlchemyError as e:
        db.rollback()  # Roll back in case of error to maintain DB integrity
        logger.error("controller", "create_agent", f"Database error creating agent: {str(e)}", session_id)
        raise HTTPException(status_code=500, detail=str(e))  # Return error to client

# ✅ Function to get all agents from the database with creator info (via join)
def get_agents(db: Session, session_id: str = ""):
    try:
        log_info("controller", "get_agents", "Retrieving all agents from database", session_id)

        # joinedload eager-loads the 'created_by_user' relationship to reduce queries
        agents = db.query(Agent).options(joinedload(Agent.created_by_user)).all()

        log_info("controller", "get_agents", f"Successfully retrieved {len(agents)} agents", session_id)
        return agents
    except SQLAlchemyError as e:
        log_error("controller", "get_agents", f"Database error retrieving agents: {str(e)}", session_id)
        raise HTTPException(status_code=500, detail=str(e))  # Return DB error as HTTP 500

# ✅ Function to fetch a single agent by its ID
def get_agent(db: Session, agent_id: int, session_id: str = ""):
    try:
        log_info("controller", "get_agent", f"Retrieving agent with ID: {agent_id}", session_id)

        agent = db.query(Agent).filter(Agent.id == agent_id).first()

        if agent:
            log_info("controller", "get_agent", f"Successfully retrieved agent: {agent.name}", session_id)
        else:
            log_info("controller", "get_agent", f"Agent with ID {agent_id} not found", session_id)

        return agent  # Returns None if not found
    except SQLAlchemyError as e:
        log_error("controller", "get_agent", f"Database error retrieving agent {agent_id}: {str(e)}", session_id)
        raise HTTPException(status_code=500, detail=str(e))

# ✅ Function to update an existing agent by ID
def update_agent(db: Session, agent_id: int, updated_data: AgentUpdate, session_id: str = ""):
    try:
        log_info("controller", "update_agent", f"Updating agent with ID: {agent_id}", session_id)

        agent = db.query(Agent).filter(Agent.id == agent_id).first()  # Fetch agent
        if agent is None:
            log_info("controller", "update_agent", f"Agent with ID {agent_id} not found for update", session_id)
            return None  # Return None if agent doesn't exist

        # Update only provided fields using exclude_unset=True
        updated_fields = updated_data.dict(exclude_unset=True)
        for field, value in updated_fields.items():
            setattr(agent, field, value)

        db.commit()       # Commit the changes
        db.refresh(agent) # Refresh to get updated data

        log_info("controller", "update_agent", f"Successfully updated agent {agent_id}: {list(updated_fields.keys())}", session_id)
        return agent
    except SQLAlchemyError as e:
        db.rollback()
        log_error("controller", "update_agent", f"Database error updating agent {agent_id}: {str(e)}", session_id)
        raise HTTPException(status_code=500, detail=str(e))

# ✅ Function to delete an agent by ID
def delete_agent(db: Session, agent_id: int, session_id: str = ""):
    try:
        log_info("controller", "delete_agent", f"Deleting agent with ID: {agent_id}", session_id)

        agent = db.query(Agent).filter(Agent.id == agent_id).first()  # Find agent
        if agent is None:
            log_info("controller", "delete_agent", f"Agent with ID {agent_id} not found for deletion", session_id)
            return None  # Return None if not found

        agent_name = agent.name  # Store name for logging before deletion
        db.delete(agent)  # Mark agent for deletion
        db.commit()       # Persist the deletion

        log_info("controller", "delete_agent", f"Successfully deleted agent {agent_id}: {agent_name}", session_id)
        return True       # Indicate successful deletion
    except SQLAlchemyError as e:
        db.rollback()
        log_error("controller", "delete_agent", f"Database error deleting agent {agent_id}: {str(e)}", session_id)
        raise HTTPException(status_code=500, detail=str(e))
