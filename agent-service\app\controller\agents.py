from sqlalchemy.orm import Session, joinedload
from sqlalchemy.exc import SQLAlchemyError
from fastapi import HTTPException
from app.models.agent import Agent
from app.schemas.agents import AgentCreate, AgentUpdate

# ✅ Function to create a new agent entry in the database
def create_agent(db: Session, agent: AgentCreate):
    try:
        # Convert Pydantic schema to ORM model instance
        db_agent = Agent(**agent.dict())
        db.add(db_agent)  # Add agent to session
        db.commit()       # Commit transaction to persist in DB
        db.refresh(db_agent)  # Refresh instance with DB-generated values (e.g., ID)
        return db_agent
    except SQLAlchemyError as e:
        db.rollback()  # Roll back in case of error to maintain DB integrity
        raise HTTPException(status_code=500, detail=str(e))  # Return error to client

# ✅ Function to get all agents from the database with creator info (via join)
def get_agents(db: Session):
    try:
        # joinedload eager-loads the 'created_by_user' relationship to reduce queries
        return db.query(Agent).options(joinedload(Agent.created_by_user)).all()
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=str(e))  # Return DB error as HTTP 500

# ✅ Function to fetch a single agent by its ID
def get_agent(db: Session, agent_id: int):
    try:
        return db.query(Agent).filter(Agent.id == agent_id).first()
        # Returns None if not found
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=str(e))

# ✅ Function to update an existing agent by ID
def update_agent(db: Session, agent_id: int, updated_data: AgentUpdate):
    try:
        agent = db.query(Agent).filter(Agent.id == agent_id).first()  # Fetch agent
        if agent is None:
            return None  # Return None if agent doesn't exist
        # Update only provided fields using exclude_unset=True
        for field, value in updated_data.dict(exclude_unset=True).items():
            setattr(agent, field, value)
        db.commit()       # Commit the changes
        db.refresh(agent) # Refresh to get updated data
        return agent
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

# ✅ Function to delete an agent by ID
def delete_agent(db: Session, agent_id: int):
    try:
        agent = db.query(Agent).filter(Agent.id == agent_id).first()  # Find agent
        if agent is None:
            return None  # Return None if not found
        db.delete(agent)  # Mark agent for deletion
        db.commit()       # Persist the deletion
        return True       # Indicate successful deletion
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))
