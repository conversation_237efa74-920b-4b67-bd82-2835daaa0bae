# Import required FastAPI, JWT, SQLAlchemy, and internal modules
from fastapi import Depends, HTTPException, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import jwt, J<PERSON><PERSON>rror
from sqlalchemy.orm import Session
from app.database.connection import <PERSON><PERSON>ocal
from app.config import SECRET_KEY, ALGORITHM
from app.controllers import user as crud_user

# Initialize HTTPBearer security scheme for extracting token via Swagger UI
security = HTTPBearer()

# Get database session
def get_db():
    #Creates and yields a SQLAlchemy database session 
    #Ensures the session is properly closed after use.
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Extract bearer token from Authorization header (your original method)
def get_token_from_header(request: Request):
    #Extracts JWT token from the 'Authorization' header in a raw format
    #  Raises HTTPException if the header is missing or malformed.
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Invalid authorization header")
    return auth_header.split(" ")[1]

# Alternative method using <PERSON>TT<PERSON>Bearer for Swagger UI compatibility
def get_token_from_bearer(credentials: HTTPAuthorizationCredentials = Depends(security)):

     #Extracts the bearer token using FastAPI’s HTTPBearer dependency 
    # This works seamlessly with Swagger UI.
    return credentials.credentials


# Main dependency to get the current user from token
# Use HTTPBearer method for better Swagger UI support
def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    #Decodes the JWT token, validates it, and fetches the associated user 
    # Uses HTTPBearer for token extraction (compatible with Swagger UI) 
    # Raises HTTP 401 if token is invalid or user not found.
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials"
    )
    try:
         # Decode JWT token using secret key and algorithm
        # Extract token from HTTPBearer credentials
        token = credentials.credentials
        
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        email = payload.get("sub")
        if email is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    
     # Fetch user from DB using email from token payload
    user = crud_user.get_user_by_email(db, email)
    if user is None:
        raise credentials_exception

    return user

# Optional alternative version that uses manual header parsing instead of HTTPBearer
# Alternative version using your original method (if you prefer)
def get_current_user_custom_header(
    token: str = Depends(get_token_from_header),
    db: Session = Depends(get_db)
):
    # Decodes JWT token manually extracted from the 'Authorization' header.
    # Useful if not relying on FastAPI's HTTPBearer.
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials"
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        email = payload.get("sub")
        if email is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception

    user = crud_user.get_user_by_email(db, email)
    if user is None:
        raise credentials_exception

    return user