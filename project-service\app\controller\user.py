from sqlalchemy.orm import Session
from app.models.user import User,UserRole, Role
from app.schemas.user import UserCreate,UserUpdate
from passlib.hash import bcrypt
from datetime import datetime
from sqlalchemy.orm import joinedload

# Retrieves a user by email from the DB.
def get_user_by_email(db: Session, email: str):
    user = db.query(User).options(joinedload(User.roles)).filter(User.email == email).first()
    return user

# Hashes the user's password with bcrypt.
# Creates a User record.
# Assigns a default role (via UserRole join table).
def create_user(db: Session, user: UserCreate, default_role="admin"):
    hashed_pw = bcrypt.hash(user.password)
    db_user = User(
        name=user.name,
        email=user.email,
        password_hash=hashed_pw,
        organization_id=user.organization_id,
        is_account_holder=False,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)

    # 🔄 Assign default role
    role = db.query(Role).filter(Role.name == default_role).first()
    if role:
        db_user_role = UserRole(user_id=db_user.id, role_id=role.id)
        db.add(db_user_role)
        db.commit()

    return db_user

# Verifies a user exists and checks password hash.

def verify_user(db: Session, email: str, password: str):
    user = get_user_by_email(db, email)
    if not user:
        return None
    if not bcrypt.verify(password, user.password_hash):
        return None
    return user

# Fetches the user's assigned role from the join between UserRole and Role.
def get_user_roles(db: Session, user_id: int):
    result = (
        db.query(Role.name)
        .join(UserRole, Role.id == UserRole.role_id)
        .filter(UserRole.user_id == user_id)
        .first()
    )
    return result[0] if result else None

def get_user_role(db: Session, user_id: int) -> str:
    result = db.query(Role.name).join(UserRole).filter(UserRole.user_id == user_id).first()
    return result[0] if result else "user"  # default to 'user'

def get_all_users(db: Session):
    return db.query(User).all()

# Get single user by ID
def get_user_by_id(db: Session, user_id: int):
    return db.query(User).filter(User.id == user_id).first()


def update_user(db: Session, user_id: int, update_data: UserUpdate):
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        return None
    for field, value in update_data.dict(exclude_unset=True).items():
        setattr(user, field, value)
    db.commit()
    db.refresh(user)
    return user