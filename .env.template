# Centralized MongoDB Logging Configuration
# Copy this to your main .env file and update the values

# MongoDB Configuration for Centralized Logging
MONGODB_URL=mongodb://localhost:27017
MONGODB_DATABASE=agent_logs
MONGODB_COLLECTION=logs
MONGODB_SYNC_INTERVAL=10

# Logging Configuration
LOG_BASE_PATH=logs
LOG_FILE_EXTENSION=.log
DEBUG_LOGGING=false

# Service Configuration (optional)
DEFAULT_SERVICE_NAME=unknown-service

# Database Configuration (for individual services)
DATABASE_URL=postgresql://username:password@localhost:5432/your_database

# JWT Configuration (for auth services)
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
