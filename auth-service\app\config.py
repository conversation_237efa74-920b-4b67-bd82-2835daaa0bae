import os
from dotenv import load_dotenv

# ✅ Correct: Only load once here
load_dotenv()

SECRET_KEY = os.getenv("SECRET_KEY")
ALGORITHM = os.getenv("ALGORITHM")
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES"))
DATABASE_URL = os.getenv("DATABASE_URL")

# ✅ MongoDB configuration for logging
MONGODB_URL = os.getenv("MONGODB_URL")
MONGODB_DATABASE = os.getenv("MONGODB_DATABASE")
MONGODB_COLLECTION = os.getenv("MONGODB_COLLECTION")
MONGODB_SYNC_INTERVAL = int(os.getenv("MONGODB_SYNC_INTERVAL"))  # Configurable sync interval

print("🔍 DATABASE_URL from .env:", DATABASE_URL)
print("🔍 MONGODB_URL from .env:", MONG<PERSON>B_URL)
