from sqlalchemy import Column, String,BigInteger,TIMESTAMP,Text
from app.database.connection import Base
from sqlalchemy.orm import relationship


class Organization(Base):
    __tablename__ = "organizations"
    
    id = Column(BigInteger, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    created_at = Column(TIMESTAMP, nullable=False)
    updated_at = Column(TIMESTAMP, nullable=False)

    projects = relationship("Project", back_populates="organization")