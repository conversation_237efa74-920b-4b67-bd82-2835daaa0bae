import os
from dotenv import load_dotenv

# ✅ Load environment variables from a .env file into the process environment
load_dotenv()

# ✅ Load secret key used for signing JWTs or other cryptographic functions
SECRET_KEY = os.getenv("SECRET_KEY")

# ✅ Define the algorithm used for JWT encoding/decoding (e.g., HS256)
ALGORITHM = os.getenv("ALGORITHM")

# ✅ Token expiry duration in minutes (default: 30 if not set in .env)
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", 30))

# ✅ Database connection URL (e.g., PostgreSQL or SQLite connection string)
DATABASE_URL = os.getenv("DATABASE_URL")

# ✅ Print statement for debugging to confirm .env loading (optional; remove in production)
print("🔍 DATABASE_URL from .env:", DATABASE_URL)
