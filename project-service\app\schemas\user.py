from pydantic import BaseModel, EmailStr
from typing import List, Optional

class UserCreate(BaseModel):
    name: str
    email: EmailStr
    password: str
    organization_id: int

class UserOut(BaseModel):
    id: int
    name: str
    email: EmailStr
    is_account_holder: bool

    class Config:
        orm_mode = True

class Token(BaseModel):
    access_token: str
    token_type: str


class RegisterResponse(BaseModel):
    message: str
    access_token: str
    token_type: str


class Login(BaseModel):
    # name: str
    email: EmailStr
    password: str



class RefreshTokenRequest(BaseModel):
    refresh_token: str


class UserInfo(BaseModel):
    id: int
    name: str
    email: str
    organization_id: Optional[int]
    roles: List[str]

class UserUpdate(BaseModel):
    name: Optional[str] = None
    email: Optional[str] = None
    organization_id: Optional[int] = None