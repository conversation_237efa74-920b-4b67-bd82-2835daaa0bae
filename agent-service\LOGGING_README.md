# MongoDB Logging System for Agent Service

## Overview

This logging system provides enterprise-level MongoDB logging with file buffering and background synchronization. It's designed to be clean, precise, and minimally invasive to existing code.

## Features

- **File Buffering**: High-performance logging with local file buffering
- **Background Sync**: Automatic MongoDB synchronization every 5 seconds
- **Thread-Safe**: Safe for concurrent use across the application
- **Error Handling**: Graceful handling of MongoDB connection issues
- **Minimal Integration**: Clean integration with existing controller and router code

## Log Format

Logs follow the specified format:

```json
{
  "_id": "68753f21a9b40966a0bf2d82",
  "source": "controller",
  "timestamp": "2025-01-15T10:30:45.123Z",
  "date": "2025-01-15",
  "service": "agent-service",
  "component": "create_agent",
  "level": "INFO",
  "message": "Successfully created agent with ID: 123",
  "sessionId": "session-a49db70c6643a30ed043c5a276e0e793",
  "metadata": {
    "status_code": 200,
    "agent_id": 123
  }
}
```

## Configuration

Add these environment variables to your `.env` file:

```env
# MongoDB Configuration for Logging
MONGODB_URL=mongodb://localhost:27017
MONGODB_DATABASE=agent_logs
MONGODB_COLLECTION=logs
MONGODB_SYNC_INTERVAL=10  # Sync interval in seconds (default: 10)
```

## Dependencies

The system requires `pymongo==4.6.1` which has been added to `requirements_new.txt`.

## Usage

### In Controllers

```python
from app.utils.mongo_logger import log_info, log_error

def create_agent(db: Session, agent: AgentCreate):
    try:
        log_info("controller", "create_agent", f"Creating new agent: {agent.name}")
        # ... business logic ...
        log_info("controller", "create_agent", f"Successfully created agent with ID: {db_agent.id}")
        return db_agent
    except Exception as e:
        log_error("controller", "create_agent", f"Error creating agent: {str(e)}")
        raise
```

### In Routers

```python
from app.utils.mongo_logger import log_info, log_error

@router.post("/agents/", response_model=AgentOut)
def create_agents(agent: AgentCreate, db: Session = Depends(get_db)):
    try:
        log_info("router", "create_agents", "POST /agents/ called")
        result = create_agent(db, agent)
        log_info("router", "create_agents", "Successfully created agent via API", 
                metadata={"status_code": 200, "agent_id": result.id})
        return result
    except HTTPException as e:
        log_error("router", "create_agents", f"HTTP error: {e.detail}", 
                 metadata={"status_code": e.status_code})
        raise
```

## Log Levels

- **INFO**: Successful operations, API calls, normal flow
- **ERROR**: Errors, exceptions, failed operations
- **WARNING**: Warnings, potential issues

## File Structure

```
agent-service/
├── app/
│   └── utils/
│       └── mongo_logger.py          # Main logging utility
├── logs/
│   └── agent_service.log            # Temporary log buffer file
├── test_logging.py                  # Test script
└── LOGGING_README.md               # This file
```

## Testing

Run the test script to verify the logging system:

```bash
cd agent-service
python test_logging.py
```

The test script will:
1. Test file buffering functionality
2. Verify log format compliance
3. Test MongoDB connection
4. Check background synchronization

## How It Works

1. **Logging Call**: Application calls `log_info()`, `log_error()`, etc.
2. **File Buffer**: Log is immediately written to `logs/agent_service.log`
3. **Background Thread**: At configurable intervals (default 10 seconds), a background thread:
   - Reads all logs from the file
   - Inserts them into MongoDB
   - Clears the file for new logs
4. **Error Handling**: If MongoDB is unavailable, logs remain in file until connection is restored

## MongoDB Setup

Ensure MongoDB is running and accessible. The system will automatically:
- Create the `agent_logs` database if it doesn't exist
- Create the `logs` collection if it doesn't exist
- Handle connection failures gracefully

## Performance

- **File I/O**: Minimal impact with efficient file operations
- **Memory Usage**: Low memory footprint with immediate file writes
- **Network**: Batch inserts to MongoDB reduce network overhead
- **Threading**: Single background thread for all sync operations

## Monitoring

Check the following for system health:
- Log file size (should be small if sync is working)
- MongoDB collection document count
- Application console for sync status messages

## Troubleshooting

### Logs not appearing in MongoDB
1. Check MongoDB connection string in `.env`
2. Verify MongoDB is running
3. Check console for connection error messages
4. Run `test_logging.py` to diagnose issues

### High log file size
1. Check MongoDB connectivity
2. Verify background sync thread is running
3. Check for MongoDB authentication issues

### Missing log fields
1. Verify log format in `mongo_logger.py`
2. Check that all required parameters are passed to logging functions
