from fastapi import FastAPI
from app.routes.agent_router import router as agent_router
from app.database.connection import Base, engine

# ✅ Create the FastAPI application instance
app = FastAPI(title="Agent Service", version="1.0.0")

# ✅ Automatically create all tables defined by SQLAlchemy models (only for dev/test)
# This executes Base.metadata.create_all() using the connected engine
Base.metadata.create_all(bind=engine)

# ✅ Register the agent router with all its endpoints (prefixed with /agents)
app.include_router(agent_router)

# ✅ Health check endpoint – useful for monitoring tools and uptime checks
@app.get("/health")
def health_check():
    return {"status": "healthy", "service": "agent-service"}

# ✅ Run the application using Uvicorn when this script is executed directly
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)  # Makes the app accessible on port 8002
