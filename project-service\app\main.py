from fastapi import FastAPI
from app.models import *
from app.routes.project import router as project_router
from app.database.connection import Base, engine


app = FastAPI(title="project Service", version="1.0.0")

Base.metadata.create_all(bind=engine)
app.include_router(project_router)

@app.get("/health")
def health_check():
    return {"status": "healthy", "service": "project-service"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8003)
