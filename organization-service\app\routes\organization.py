from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.controllers.organization import get_by_id, get_all, create, update, delete
from app.database.connection import get_db
from app.models.organization import Organization
from app.schemas.organization import OrganizationCreate,OrganizationUpdate,OrganizationOut
from app.dependencies.auth_dependencies import get_current_user
from app.models.user import User
from app.utils.permissions import user_has_permission


router = APIRouter(prefix="/organizations", tags=["Organizations"])


@router.get("/", response_model=list[OrganizationOut])
def read_all(db: Session = Depends(get_db),current_user = Depends(get_current_user)):
    if not user_has_permission(current_user.id, "read_organization", db):
        raise HTTPException(status_code=403, detail="Access denied")
    return get_all(db)

@router.get("/{org_id}", response_model=OrganizationOut)
def read_one(org_id: int, db: Session = Depends(get_db), current_user = Depends(get_current_user)):
    if not user_has_permission(current_user.id, "read_organization", db):
        raise HTTPException(status_code=403, detail="Access denied")
    org = get_by_id(db, org_id)
    if not org:
        raise HTTPException(status_code=404, detail="Organization not found")
    return org

@router.post("/", response_model=OrganizationOut)
def create_org(org: OrganizationCreate, db: Session = Depends(get_db), current_user = Depends(get_current_user)):
    if not user_has_permission(current_user.id, "create_organization", db):
        raise HTTPException(status_code=403, detail="Access denied")
    return create(db, org)

@router.put("/{org_id}", response_model=OrganizationOut)
def update_org(org_id: int, org: OrganizationUpdate, db: Session = Depends(get_db), current_user = Depends(get_current_user)):
    if not user_has_permission(current_user.id, "update_organization", db):
        raise HTTPException(status_code=403, detail="Access denied")
    updated = update(db, org_id, org)
    if not updated:
        raise HTTPException(status_code=404, detail="Organization not found")
    return updated

@router.delete("/{org_id}")
def delete_org(org_id: int, db: Session = Depends(get_db), current_user = Depends(get_current_user)):
    if not user_has_permission(current_user.id, "delete_organization", db):
        raise HTTPException(status_code=403, detail="Access denied")
    deleted = delete(db, org_id)
    if not deleted:
        raise HTTPException(status_code=404, detail="Organization not found")
    return {"message": "Organization deleted successfully"}