import uuid
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.controllers.organization import get_by_id, get_all, create, update, delete
from app.database.connection import get_db
from app.models.organization import Organization
from app.schemas.organization import OrganizationCreate,OrganizationUpdate,OrganizationOut
from app.dependencies.auth_dependencies import get_current_user
from app.models.user import User
from app.utils.permissions import user_has_permission
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'logging-service'))
from logging_service.service_logger import create_service_logger

# Create service-specific logger
logger = create_service_logger("organization-service")


router = APIRouter(prefix="/organizations", tags=["Organizations"])


@router.get("/", response_model=list[OrganizationOut])
def read_all(db: Session = Depends(get_db),current_user = Depends(get_current_user)):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        logger.info("router", "read_all", f"GET /organizations/ called by user {current_user.id}", session_id)

        if not user_has_permission(current_user.id, "read_organization", db):
            logger.error("router", "read_all", f"Access denied for user {current_user.id}", session_id,
                     metadata={"status_code": 403})
            raise HTTPException(status_code=403, detail="Access denied")

        result = get_all(db, session_id)
        logger.info("router", "read_all", f"Successfully retrieved organizations via API", session_id,
                metadata={"status_code": 200, "organization_count": len(result)})
        return result

    except HTTPException as e:
        logger.error("router", "read_all", f"HTTP error in read_all: {e.detail}", session_id,
                 metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        logger.error("router", "read_all", f"Unexpected error in read_all: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/{org_id}", response_model=OrganizationOut)
def read_one(org_id: int, db: Session = Depends(get_db), current_user = Depends(get_current_user)):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        logger.info("router", "read_one", f"GET /organizations/{org_id} called by user {current_user.id}", session_id)

        if not user_has_permission(current_user.id, "read_organization", db):
            logger.error("router", "read_one", f"Access denied for user {current_user.id}", session_id,
                     metadata={"status_code": 403})
            raise HTTPException(status_code=403, detail="Access denied")

        org = get_by_id(db, org_id, session_id)
        if not org:
            logger.error("router", "read_one", f"Organization {org_id} not found", session_id,
                     metadata={"status_code": 404})
            raise HTTPException(status_code=404, detail="Organization not found")

        logger.info("router", "read_one", f"Successfully retrieved organization {org_id} via API", session_id,
                metadata={"status_code": 200, "organization_name": org.name})
        return org

    except HTTPException as e:
        logger.error("router", "read_one", f"HTTP error in read_one: {e.detail}", session_id,
                 metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        logger.error("router", "read_one", f"Unexpected error in read_one: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/", response_model=OrganizationOut)
def create_org(org: OrganizationCreate, db: Session = Depends(get_db), current_user = Depends(get_current_user)):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        logger.info("router", "create_org", f"POST /organizations/ called by user {current_user.id}", session_id)

        if not user_has_permission(current_user.id, "create_organization", db):
            logger.error("router", "create_org", f"Access denied for user {current_user.id}", session_id,
                     metadata={"status_code": 403})
            raise HTTPException(status_code=403, detail="Access denied")

        result = create(db, org, session_id)
        logger.info("router", "create_org", f"Successfully created organization via API", session_id,
                metadata={"status_code": 200, "organization_id": result.id})
        return result

    except HTTPException as e:
        logger.error("router", "create_org", f"HTTP error in create_org: {e.detail}", session_id,
                 metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        logger.error("router", "create_org", f"Unexpected error in create_org: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")

@router.put("/{org_id}", response_model=OrganizationOut)
def update_org(org_id: int, org: OrganizationUpdate, db: Session = Depends(get_db), current_user = Depends(get_current_user)):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        logger.info("router", "update_org", f"PUT /organizations/{org_id} called by user {current_user.id}", session_id)

        if not user_has_permission(current_user.id, "update_organization", db):
            logger.error("router", "update_org", f"Access denied for user {current_user.id}", session_id,
                     metadata={"status_code": 403})
            raise HTTPException(status_code=403, detail="Access denied")

        updated = update(db, org_id, org, session_id)
        if not updated:
            logger.error("router", "update_org", f"Organization {org_id} not found for update", session_id,
                     metadata={"status_code": 404})
            raise HTTPException(status_code=404, detail="Organization not found")

        logger.info("router", "update_org", f"Successfully updated organization {org_id} via API", session_id,
                metadata={"status_code": 200, "organization_name": updated.name})
        return updated

    except HTTPException as e:
        logger.error("router", "update_org", f"HTTP error in update_org: {e.detail}", session_id,
                 metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        logger.error("router", "update_org", f"Unexpected error in update_org: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")

@router.delete("/{org_id}")
def delete_org(org_id: int, db: Session = Depends(get_db), current_user = Depends(get_current_user)):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        logger.info("router", "delete_org", f"DELETE /organizations/{org_id} called by user {current_user.id}", session_id)

        if not user_has_permission(current_user.id, "delete_organization", db):
            logger.error("router", "delete_org", f"Access denied for user {current_user.id}", session_id,
                     metadata={"status_code": 403})
            raise HTTPException(status_code=403, detail="Access denied")

        deleted = delete(db, org_id, session_id)
        if not deleted:
            logger.error("router", "delete_org", f"Organization {org_id} not found for deletion", session_id,
                     metadata={"status_code": 404})
            raise HTTPException(status_code=404, detail="Organization not found")

        logger.info("router", "delete_org", f"Successfully deleted organization {org_id} via API", session_id,
                metadata={"status_code": 200})
        return {"message": "Organization deleted successfully"}

    except HTTPException as e:
        logger.error("router", "delete_org", f"HTTP error in delete_org: {e.detail}", session_id,
                 metadata={"status_code": e.status_code})
        raise
    except Exception as e:
        logger.error("router", "delete_org", f"Unexpected error in delete_org: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")