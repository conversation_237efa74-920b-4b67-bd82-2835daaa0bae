from sqlalchemy import <PERSON>umn, Inte<PERSON>, String, DateTime, BigInteger
from sqlalchemy.sql import func
from app.database.connection import Base
from sqlalchemy.orm import relationship
from sqlalchemy import ForeignKey

# ✅ Agent model definition (represents the 'agents' table in the DB)
class Agent(Base):
    __tablename__ = "agents"  # Name of the table in the database

    # ✅ Primary key for the agent table (auto-incrementing)
    id = Column(Integer, primary_key=True, index=True)

    # ✅ Agent's basic details
    name = Column(String)             # Name of the agent
    description = Column(String)      # Description about the agent
    image_url = Column(String)        # Link to the agent's image
    voice_url = Column(String)        # Link to the agent's voice file
    os_path = Column(String)          # Local file system path for agent files
    gender = Column(String)           # Gender of the agent (optional use)

    # ✅ Timestamps (automatically handled by SQLAlchemy)
    created_at = Column(DateTime, server_default=func.now())  # Set on creation
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())  # Updated on modification

    # ✅ Foreign key: Who created this agent (points to users.id)
    created_by_user_id = Column(<PERSON><PERSON><PERSON>ger, ForeignKey("users.id"))

    # ✅ Relationship: Link to the `User` model who created this agent
    # This sets up a reverse relationship from Agent to User
    created_by_user = relationship("User", back_populates="agents")
