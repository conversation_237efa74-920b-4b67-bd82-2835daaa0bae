# docker-compose.yml
version: '3.8'

services:
  # Databases
  auth-db:
    image: postgres:13
    environment:
      POSTGRES_DB: auth_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - auth_db_data:/var/lib/postgresql/data

  user-db:
    image: postgres:13
    environment:
      POSTGRES_DB: user_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - user_db_data:/var/lib/postgresql/data

  agent-db:
    image: postgres:13
    environment:
      POSTGRES_DB: agent_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - agent_db_data:/var/lib/postgresql/data

  # Services
  auth-service:
    build: ./auth-service
    ports:
      - "8001:8000"
    environment:
      DATABASE_URL: *******************************************/auth_db
    depends_on:
      - auth-db

  user-service:
    build: ./user-service
    ports:
      - "8002:8000"
    environment:
      DATABASE_URL: *******************************************/user_db
      AUTH_SERVICE_URL: http://auth-service:8000
    depends_on:
      - user-db
      - auth-service

  agent-service:
    build: ./agent-service
    ports:
      - "8003:8000"
    environment:
      DATABASE_URL: ********************************************/agent_db
      AUTH_SERVICE_URL: http://auth-service:8000
    depends_on:
      - agent-db
      - auth-service

  api-gateway:
    build: ./api-gateway
    ports:
      - "8000:8000"
    environment:
      AUTH_SERVICE_URL: http://auth-service:8000
      USER_SERVICE_URL: http://user-service:8000
      AGENT_SERVICE_URL: http://agent-service:8000
    depends_on:
      - auth-service
      - user-service
      - agent-service

volumes:
  auth_db_data:
  agent_db_data: