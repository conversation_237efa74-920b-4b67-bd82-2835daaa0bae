import uuid
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from app.database.connection import get_db
from app.schemas.workflow_approval import WorkflowApprovalCreate, WorkflowApprovalUpdate, WorkflowApprovalOut
from app.controller.workflow_approval import (
    create_workflow_approval, get_approval_by_id, update_approval,
    delete_approval, get_all_approvals
)
from app.dependencies.auth_dependencies import get_current_user
from app.utils.permission import user_has_permission
from app.utils.mongo_logger import log_info, log_error

router = APIRouter(prefix="/workflow-approvals", tags=["Workflow Approvals"])

# 🚀 Create a new workflow approval
@router.post("/", response_model=WorkflowApprovalOut)
def create_approval(
    approval: WorkflowApprovalCreate,
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    # Generate unique session ID for this request
    session_id = f"session-{uuid.uuid4().hex}"

    try:
        log_info("router", "create_approval", f"POST /workflow-approvals/ called by user {current_user.id}", session_id)

        # ✅ Permission check
        if not user_has_permission(current_user.id, "create_workflow", db):
            log_error("router", "create_approval", f"Access denied for user {current_user.id}", session_id,
                     metadata={"status_code": 403})
            raise HTTPException(status_code=403, detail="Access denied")

        # ✅ Proceed with creation
        result = create_workflow_approval(db, approval, session_id)
        log_info("router", "create_approval", f"Successfully created workflow approval via API", session_id,
                metadata={"status_code": 200, "approval_id": result.id})
        return result

    except HTTPException as e:
        log_error("router", "create_approval", f"HTTP error in create_approval: {e.detail}", session_id,
                 metadata={"status_code": e.status_code})
        raise
    except SQLAlchemyError as e:
        # ❌ Handle database exceptions
        log_error("router", "create_approval", f"Database error in create_approval: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail=f"Failed to create approval: {str(e)}")
    except Exception as e:
        log_error("router", "create_approval", f"Unexpected error in create_approval: {str(e)}", session_id,
                 metadata={"status_code": 500})
        raise HTTPException(status_code=500, detail="Internal server error")
    

# 📄 Get all workflow approvals
@router.get("/", response_model=list[WorkflowApprovalOut])
def read_all_approvals(
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    try:
        # ✅ Check if the user has permission to read workflows
        if not user_has_permission(current_user.id, "read_workflow", db):
            raise HTTPException(status_code=403, detail="Access denied")
        
        # ✅ Fetch and return all approvals
        return get_all_approvals(db)

    except SQLAlchemyError as e:
        #  Handle database-related exceptions
        raise HTTPException(status_code=500, detail=f"Failed to fetch approvals: {str(e)}")


# 🔍 Get a single approval by ID
@router.get("/{approval_id}", response_model=WorkflowApprovalOut)
def read_approval(
    approval_id: int,
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    try:
        # 🔒 Check if the user has permission to read workflow approvals
        if not user_has_permission(current_user.id, "read_workflow", db):
            raise HTTPException(status_code=403, detail="Access denied")

        # ✅ Fetch approval
        approval = get_approval_by_id(db, approval_id)
        if not approval:
            raise HTTPException(status_code=404, detail="Approval not found")

        return approval

    except SQLAlchemyError as e:
        #Handle DB access issues
        raise HTTPException(status_code=500, detail=f"Error reading approval: {str(e)}")


# 🛠️ Update an existing approval
@router.put("/{approval_id}", response_model=WorkflowApprovalOut)
def update_approval_route(approval_id: int, update_data: WorkflowApprovalUpdate, db: Session = Depends(get_db),current_user=Depends(get_current_user)):
    try:
        # 🔒 Permission check
        if not user_has_permission(current_user.id, "update_workflow", db):
            raise HTTPException(status_code=403, detail="Access denied")

        # 🔄 Attempt update
        updated = update_approval(db, approval_id, update_data)
        if not updated:
            raise HTTPException(status_code=404, detail="Approval not found")

        return updated

    except SQLAlchemyError as e:
        #  Handle DB-level failures
        raise HTTPException(status_code=500, detail=f"Failed to update approval: {str(e)}")


# 🗑️ Delete an approval by ID
@router.delete("/{approval_id}")
def delete_approval_route(approval_id: int, db: Session = Depends(get_db),current_user=Depends(get_current_user)):
    try:
        # 🔒 Check if user has permission to delete
        if not user_has_permission(current_user.id, "delete_workflow", db):
            raise HTTPException(status_code=403, detail="Access denied")

        # 🚮 Attempt to delete the approval
        deleted = delete_approval(db, approval_id)
        if not deleted:
            raise HTTPException(status_code=404, detail="Approval not found")

        return {"message": "Approval deleted successfully"}

    except SQLAlchemyError as e:
        # Handle DB deletion failure
        raise HTTPException(status_code=500, detail=f"Failed to delete approval: {str(e)}")
