from fastapi import FastAPI
from app.database.connection import Base, engine
from app.models.workflow import Workflow
from app.models.organization import Organization
from app.routes.workflow_router import router as workflow_router
from app.routes.workflow_step_router import router as workflow_step_router
from app.routes.workflow_approval_router import router as workflow_approval_router

app = FastAPI(title="Workflow Service")

# Ensure all models are imported before creating metadata
Base.metadata.create_all(bind=engine)

app.include_router(workflow_router)
app.include_router(workflow_approval_router)
app.include_router(workflow_step_router)

@app.get("/health")
def health():
    return {"status": "ok"}
