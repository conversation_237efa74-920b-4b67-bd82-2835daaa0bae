"""
Test script for MongoDB logging functionality in Auth Service

This script tests the logging system to ensure:
1. Logs are properly buffered to file
2. Logs are synced to MongoDB
3. Log format is correct
4. Background thread works properly
"""

import time
import json
from pathlib import Path
from app.utils.mongo_logger import log_info, log_error, log_warning, logger


def test_file_buffering():
    """Test that logs are written to file buffer"""
    print("Testing file buffering...")
    
    # Clear any existing log file
    log_file = Path("logs/auth_service.log")
    if log_file.exists():
        log_file.unlink()
    
    # Generate test logs
    log_info("test", "file_buffering", "Test info message", "test-session-123", {"test": "data"})
    log_error("test", "file_buffering", "Test error message", "test-session-123", {"error": "test_error"})
    log_warning("test", "file_buffering", "Test warning message", "test-session-123")
    
    # Check if file exists and has content
    time.sleep(0.1)  # Small delay to ensure file write
    
    if log_file.exists():
        with open(log_file, 'r') as f:
            content = f.read()
            if content:
                print("✅ File buffering works - logs written to file")
                
                # Parse and display log entries
                lines = content.strip().split('\n')
                for i, line in enumerate(lines, 1):
                    try:
                        log_entry = json.loads(line)
                        print(f"   Log {i}: {log_entry['level']} - {log_entry['message']}")
                    except json.JSONDecodeError:
                        print(f"   Log {i}: Invalid JSON format")
            else:
                print("❌ File buffering failed - file is empty")
    else:
        print("❌ File buffering failed - log file not created")


def test_log_format():
    """Test that log format matches the required structure"""
    print("\nTesting log format...")
    
    log_file = Path("logs/auth_service.log")
    if log_file.exists():
        with open(log_file, 'r') as f:
            lines = f.readlines()
            if lines:
                try:
                    log_entry = json.loads(lines[0].strip())
                    
                    # Check required fields
                    required_fields = ["_id", "source", "timestamp", "date", "service", 
                                     "component", "level", "message", "sessionId", "metadata"]
                    
                    missing_fields = [field for field in required_fields if field not in log_entry]
                    
                    if not missing_fields:
                        print("✅ Log format is correct - all required fields present")
                        print(f"   Sample log structure: {list(log_entry.keys())}")
                        print(f"   Service: {log_entry.get('service')}")
                    else:
                        print(f"❌ Log format incorrect - missing fields: {missing_fields}")
                        
                except json.JSONDecodeError:
                    print("❌ Log format incorrect - invalid JSON")
            else:
                print("❌ No logs to test format")
    else:
        print("❌ No log file to test format")


def test_background_sync():
    """Test that background sync thread is working"""
    print("\nTesting background sync...")
    
    # Generate more logs
    for i in range(3):
        log_info("test", "background_sync", f"Auth sync test message {i+1}", f"sync-session-{i}")
    
    print("Generated 3 test logs, waiting for background sync...")
    
    # Get sync interval from config
    try:
        from app.config import MONGODB_SYNC_INTERVAL
        sync_interval = MONGODB_SYNC_INTERVAL
    except:
        sync_interval = 10  # Default fallback
    
    print(f"Note: Background sync runs every {sync_interval} seconds")
    print("Check MongoDB collection 'logs' in database 'agent_logs' for synced logs")
    
    # Wait a bit to see if sync happens (sync interval + 1 second buffer)
    time.sleep(sync_interval + 1)
    
    log_file = Path("logs/auth_service.log")
    if log_file.exists():
        file_size = log_file.stat().st_size
        if file_size == 0:
            print("✅ Background sync appears to be working - log file was cleared")
        else:
            print("⚠️  Background sync may not be working - log file still has content")
            print("   This could be due to MongoDB connection issues")
    else:
        print("⚠️  Cannot determine sync status - log file doesn't exist")


def test_mongodb_connection():
    """Test MongoDB connection"""
    print("\nTesting MongoDB connection...")
    
    try:
        from pymongo import MongoClient
        from app.config import MONGODB_URL, MONGODB_DATABASE
        
        client = MongoClient(MONGODB_URL, serverSelectionTimeoutMS=5000)
        # Test connection
        client.admin.command('ping')
        
        db = client[MONGODB_DATABASE]
        collections = db.list_collection_names()
        
        print("✅ MongoDB connection successful")
        print(f"   Database: {MONGODB_DATABASE}")
        print(f"   Collections: {collections}")
        
        client.close()
        
    except Exception as e:
        print(f"❌ MongoDB connection failed: {str(e)}")
        print("   Make sure MongoDB is running and connection string is correct")


def main():
    """Run all tests"""
    print("🧪 Testing MongoDB Logging System for Auth Service")
    print("=" * 60)
    
    test_file_buffering()
    test_log_format()
    test_mongodb_connection()
    test_background_sync()
    
    print("\n" + "=" * 60)
    print("Test completed. Check the results above.")
    print("\nTo verify MongoDB logging:")
    print("1. Ensure MongoDB is running")
    print("2. Check the 'agent_logs' database")
    print("3. Look for documents in the 'logs' collection")
    print("4. Filter by service: 'auth-service'")
    
    # Keep the script running for a bit to allow background sync
    print("\nKeeping script running for 10 seconds to allow background sync...")
    time.sleep(10)
    
    # Shutdown logger gracefully
    logger.shutdown()
    print("Logger shutdown complete.")


if __name__ == "__main__":
    main()
