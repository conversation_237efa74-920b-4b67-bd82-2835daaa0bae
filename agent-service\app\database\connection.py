# ✅ Import SQLAlchemy engine and session utilities
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# ✅ Import your database URL from config (usually from .env or settings file)
from app.config import DATABASE_URL

# ✅ Import declarative_base to define models (ORM base class)
from sqlalchemy.orm import declarative_base

# ✅ Define the base class for all ORM models
Base = declarative_base()

# ✅ Create a SQLAlchemy engine that manages connections to the database
# It uses the DATABASE_URL defined in your config
engine = create_engine(DATABASE_URL)

# ✅ Create a session factory
# autocommit=False → ensures explicit commits
# autoflush=False → disables automatic flushes, manual control preferred
# bind=engine → connect sessions to the created engine
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# ⚠️ Redundant line – already defined Base above. This can be removed:
# Base = declarative_base()

# ✅ Dependency to get a database session (used with FastAPI's Depends)
def get_db():
    db = SessionLocal()  # Create a new session
    try:
        yield db  # Pass session to the request handler
    finally:
        db.close()  # Ensure session is closed after use (even on exceptions)
