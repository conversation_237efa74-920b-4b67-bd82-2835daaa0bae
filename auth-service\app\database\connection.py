from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.config import DATABASE_URL #  Import the database URL from config
from sqlalchemy.orm import declarative_base # ✅ Recommended base class for models

# Define the base class that all ORM models will inherit from
Base = declarative_base()

# Create the SQLAlchemy engine to connect to the database
# - DATABASE_URL should follow the format: postgresql://user:password@host:port/dbname
engine = create_engine(DATABASE_URL)

#  Create a session factory
# - autocommit=False → changes must be committed manually
# - autoflush=False → prevents automatic flush before queries
# - bind=engine → link the session to the engine
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()


# ✅ Dependency to get a new DB session
# - Used in FastAPI routes with `Depends(get_db)`
# - Automatically closes the session after use
def get_db():
    db = SessionLocal()
    try:
        yield db  # Provide the session to the calling function
    finally:
        db.close() # Ensure the session is closed after use
