from sqlalchemy.orm import Session, joinedload
from sqlalchemy.exc import SQLAlchemyError
from fastapi import HTTPException

from app.models.workflow_step import WorkflowStep
from app.schemas.workflow_step import WorkflowStepCreate, WorkflowStepUpdate

# 🔹 Create a new workflow step
def create_step(db: Session, step: WorkflowStepCreate):
    try:
        db_step = WorkflowStep(**step.dict())  # Convert Pydantic model to DB model
        db.add(db_step)
        db.commit()
        db.refresh(db_step)  # Get auto-generated values like id
        return db_step
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f" Failed to create workflow step: {str(e)}")

# 📄 Retrieve all workflow steps with assigned user details
def get_all_steps(db: Session):
    try:
        return db.query(WorkflowStep).options(joinedload(WorkflowStep.assigned_user)).all()
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f" Failed to fetch workflow steps: {str(e)}")

# 🔍 Get a specific workflow step by ID
def get_step(db: Session, step_id: int):
    try:
        return db.query(WorkflowStep).filter(WorkflowStep.id == step_id).first()
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f" Failed to retrieve workflow step: {str(e)}")

# ✏️ Update an existing workflow step
def update_step(db: Session, step_id: int, data: WorkflowStepUpdate):
    try:
        step = db.query(WorkflowStep).filter(WorkflowStep.id == step_id).first()
        if not step:
            return None
        for key, value in data.dict(exclude_unset=True).items():
            setattr(step, key, value)
        db.commit()
        db.refresh(step)
        return step
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f" Failed to update workflow step: {str(e)}")

# 🗑️ Delete a workflow step by ID
def delete_step(db: Session, step_id: int):
    try:
        step = db.query(WorkflowStep).filter(WorkflowStep.id == step_id).first()
        if step:
            db.delete(step)
            db.commit()
            return True
        return False
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f" Failed to delete workflow step: {str(e)}")
