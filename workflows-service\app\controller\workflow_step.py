from sqlalchemy.orm import Session, joinedload
from sqlalchemy.exc import SQLAlchemyError
from fastapi import HTTPException

from app.models.workflow_step import WorkflowStep
from app.schemas.workflow_step import WorkflowStepCreate, WorkflowStepUpdate
from app.utils.mongo_logger import log_info, log_error

# 🔹 Create a new workflow step
def create_step(db: Session, step: WorkflowStepCreate, session_id: str = ""):
    try:
        log_info("controller", "create_step", f"Creating new workflow step: {step.name}", session_id)

        db_step = WorkflowStep(**step.dict())  # Convert Pydantic model to DB model
        db.add(db_step)
        db.commit()
        db.refresh(db_step)  # Get auto-generated values like id

        log_info("controller", "create_step", f"Successfully created workflow step with ID: {db_step.id}", session_id)
        return db_step
    except SQLAlchemyError as e:
        db.rollback()
        log_error("controller", "create_step", f"Database error creating workflow step: {str(e)}", session_id)
        raise HTTPException(status_code=500, detail=f" Failed to create workflow step: {str(e)}")

# 📄 Retrieve all workflow steps with assigned user details
def get_all_steps(db: Session, session_id: str = ""):
    try:
        log_info("controller", "get_all_steps", "Retrieving all workflow steps", session_id)

        steps = db.query(WorkflowStep).options(joinedload(WorkflowStep.assigned_user)).all()

        log_info("controller", "get_all_steps", f"Successfully retrieved {len(steps)} workflow steps", session_id)
        return steps
    except SQLAlchemyError as e:
        log_error("controller", "get_all_steps", f"Database error retrieving workflow steps: {str(e)}", session_id)
        raise HTTPException(status_code=500, detail=f" Failed to fetch workflow steps: {str(e)}")

# 🔍 Get a specific workflow step by ID
def get_step(db: Session, step_id: int, session_id: str = ""):
    try:
        log_info("controller", "get_step", f"Retrieving workflow step with ID: {step_id}", session_id)

        step = db.query(WorkflowStep).filter(WorkflowStep.id == step_id).first()

        if step:
            log_info("controller", "get_step", f"Successfully retrieved workflow step: {step.name}", session_id)
        else:
            log_info("controller", "get_step", f"Workflow step with ID {step_id} not found", session_id)

        return step
    except SQLAlchemyError as e:
        log_error("controller", "get_step", f"Database error retrieving workflow step {step_id}: {str(e)}", session_id)
        raise HTTPException(status_code=500, detail=f" Failed to retrieve workflow step: {str(e)}")

# ✏️ Update an existing workflow step
def update_step(db: Session, step_id: int, data: WorkflowStepUpdate, session_id: str = ""):
    try:
        log_info("controller", "update_step", f"Updating workflow step with ID: {step_id}", session_id)

        step = db.query(WorkflowStep).filter(WorkflowStep.id == step_id).first()
        if not step:
            log_info("controller", "update_step", f"Workflow step with ID {step_id} not found for update", session_id)
            return None

        updated_fields = data.dict(exclude_unset=True)
        for key, value in updated_fields.items():
            setattr(step, key, value)
        db.commit()
        db.refresh(step)

        log_info("controller", "update_step", f"Successfully updated workflow step {step_id}: {list(updated_fields.keys())}", session_id)
        return step
    except SQLAlchemyError as e:
        db.rollback()
        log_error("controller", "update_step", f"Database error updating workflow step {step_id}: {str(e)}", session_id)
        raise HTTPException(status_code=500, detail=f" Failed to update workflow step: {str(e)}")

# 🗑️ Delete a workflow step by ID
def delete_step(db: Session, step_id: int, session_id: str = ""):
    try:
        log_info("controller", "delete_step", f"Deleting workflow step with ID: {step_id}", session_id)

        step = db.query(WorkflowStep).filter(WorkflowStep.id == step_id).first()
        if step:
            step_name = step.name  # Store name for logging before deletion
            db.delete(step)
            db.commit()

            log_info("controller", "delete_step", f"Successfully deleted workflow step {step_id}: {step_name}", session_id)
            return True
        else:
            log_info("controller", "delete_step", f"Workflow step with ID {step_id} not found for deletion", session_id)
            return False
    except SQLAlchemyError as e:
        db.rollback()
        log_error("controller", "delete_step", f"Database error deleting workflow step {step_id}: {str(e)}", session_id)
        raise HTTPException(status_code=500, detail=f" Failed to delete workflow step: {str(e)}")
