# tests/test_users.py
import pytest
from fastapi.testclient import TestClient
from app.main import app
from app.database.connection import get_db
from app.models.user import User
from app.models.user import Role
from sqlalchemy.orm import Session
from passlib.hash import bcrypt

client = TestClient(app)

TEST_ADMIN_EMAIL = "<EMAIL>"
TEST_ADMIN_PASSWORD = "string"

# Seed admin user before tests
def seed_admin_user():
    db: Session = next(get_db())

    # Create role if not exists
    role = db.query(Role).filter(Role.name == "admin").first()
    if not role:
        role = Role(name="admin")
        db.add(role)
        db.commit()
        db.refresh(role)

    # Create user if not exists
    user = db.query(User).filter(User.email == TEST_ADMIN_EMAIL).first()
    if not user:
        user = User(
            name="Admin",
            email=TEST_ADMIN_EMAIL,
            password_hash=bcrypt.hash(TEST_ADMIN_PASSWORD),
            organization_id=1,
            roles=[role]  # ✅ Assign admin role
        )
        db.add(user)
        db.commit()
        db.refresh(user)

    # 🔁 Ensure admin role is still attached
    if role not in user.roles:
        user.roles.append(role)
        db.commit()

def get_token():
    seed_admin_user()
    response = client.post(
        "/auth/login",
        data={"username": TEST_ADMIN_EMAIL, "password": TEST_ADMIN_PASSWORD},
        headers={"Content-Type": "application/x-www-form-urlencoded"}
    )
    assert response.status_code == 200
    return response.json()["access_token"]

def test_get_all_users():
    token = get_token()
    response = client.get("/users/", headers={"Authorization": f"Bearer {token}"})
    assert response.status_code == 200
    assert isinstance(response.json(), list)

def test_register_user():
    token = get_token()
    response = client.post(
        "/users/",
        json={
            "name": "NewUser",
            "email": "<EMAIL>",
            "password": "pass123",
            "organization_id": 1,
            "role_ids": [1]  # adjust as needed
        },
        headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code in [200, 400]  # 400 if already exists

def test_get_single_user():
    token = get_token()
    response = client.get("/users/1", headers={"Authorization": f"Bearer {token}"})
    assert response.status_code in [200, 404]

def test_delete_user():
    token = get_token()

    # First, create a user to delete
    create_response = client.post(
        "/users/",
        json={
            "name": "TempUser",
            "email": "<EMAIL>",
            "password": "delete123",
            "organization_id": 1,
            "role_ids": [1]
        },
        headers={"Authorization": f"Bearer {token}"}
    )
    if create_response.status_code == 200:
        user_id = create_response.json()["id"]
        delete_response = client.delete(
            f"/users/{user_id}",
            headers={"Authorization": f"Bearer {token}"}
        )
        assert delete_response.status_code == 200
    else:
        assert create_response.status_code == 400
