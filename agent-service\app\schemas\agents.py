from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from app.schemas.user import UserOut  # Used to represent nested user info

# ✅ Shared base schema for agent creation and update
class AgentBase(BaseModel):
    name: str  # Agent's name (required)
    description: Optional[str] = None  # Short description about the agent
    image_url: Optional[str] = None    # URL to the agent's image
    voice_url: Optional[str] = None    # URL to the agent's voice/audio file
    os_path: Optional[str] = None      # Local or OS-specific path to agent resources
    gender: Optional[str] = None       # Gender (for personalization)
    created_by_user_id: int            # User ID of the creator

# ✅ Schema for creating a new agent (inherits from AgentBase)
class AgentCreate(AgentBase):
    pass  # No additional fields needed for creation

# ✅ Schema for reading an agent from the database (e.g., in responses)
class AgentOut(BaseModel):
    id: int  # Auto-generated unique agent ID
    name: str
    description: Optional[str] = None
    image_url: Optional[str] = None
    voice_url: Optional[str] = None
    os_path: Optional[str] = None
    gender: Optional[str] = None
    created_at: datetime  # Timestamp when the agent was created
    updated_at: datetime  # Timestamp when the agent was last updated

    # Nested user schema showing basic info about the creator
    created_by_user: Optional[UserOut] = None

    class Config:
        orm_mode = True  # ✅ Enables compatibility with SQLAlchemy models

# ✅ Schema for updating an existing agent
class AgentUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    image_url: Optional[str] = None
    voice_url: Optional[str] = None
    os_path: Optional[str] = None
    gender: Optional[str] = None
    created_by_user_id: Optional[int] = None  # Allow optional reassignment
