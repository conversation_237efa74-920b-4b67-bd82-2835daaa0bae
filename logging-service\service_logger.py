"""
Service Logger Integration Helper

This module provides easy integration helpers for microservices to use the centralized logging service.
It creates service-specific logging functions that automatically include the service name.
"""

from typing import Dict, Any
from .mongo_logger import get_logger


class ServiceLogger:
    """
    Service-specific logger wrapper that automatically includes service name in all log calls.
    """
    
    def __init__(self, service_name: str):
        """
        Initialize service logger for a specific service.
        
        Args:
            service_name: Name of the service (e.g., "agent-service")
        """
        self.service_name = service_name
        self.logger = get_logger(service_name)
    
    def info(self, source: str, component: str, message: str, 
             session_id: str = "", metadata: Dict[str, Any] = None) -> None:
        """Log INFO level message"""
        self.logger.log(
            source=source, 
            component=component, 
            level="INFO", 
            message=message,
            session_id=session_id, 
            metadata=metadata
        )
    
    def error(self, source: str, component: str, message: str, 
              session_id: str = "", metadata: Dict[str, Any] = None) -> None:
        """Log ERROR level message"""
        self.logger.log(
            source=source, 
            component=component, 
            level="ERROR", 
            message=message,
            session_id=session_id, 
            metadata=metadata
        )
    
    def warning(self, source: str, component: str, message: str, 
                session_id: str = "", metadata: Dict[str, Any] = None) -> None:
        """Log WARNING level message"""
        self.logger.log(
            source=source, 
            component=component, 
            level="WARNING", 
            message=message,
            session_id=session_id, 
            metadata=metadata
        )
    
    def debug(self, source: str, component: str, message: str, 
              session_id: str = "", metadata: Dict[str, Any] = None) -> None:
        """Log DEBUG level message"""
        self.logger.log(
            source=source, 
            component=component, 
            level="DEBUG", 
            message=message,
            session_id=session_id, 
            metadata=metadata
        )
    
    def shutdown(self) -> None:
        """Shutdown this service's logger"""
        self.logger.shutdown()


def create_service_logger(service_name: str) -> ServiceLogger:
    """
    Create a service-specific logger instance.
    
    Args:
        service_name: Name of the service (e.g., "agent-service")
    
    Returns:
        ServiceLogger instance for the service
    
    Example:
        logger = create_service_logger("agent-service")
        logger.info("router", "create_agent", "Agent created successfully", session_id="123")
    """
    return ServiceLogger(service_name)
