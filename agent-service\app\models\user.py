from sqlalchemy import Column, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>ole<PERSON>, DateTime, ForeignKey, Table, BigInteger, TIMESTAMP
from sqlalchemy.orm import relationship
from datetime import datetime
from app.database.connection import Base
from sqlalchemy.sql import func

# ✅ Association table to link users and roles (Many-to-Many)
class UserRole(Base):
    __tablename__ = "user_roles"

    # Composite primary key using user_id and role_id
    user_id = Column(Integer, ForeignKey("users.id"), primary_key=True)
    role_id = Column(Integer, ForeignKey("roles.id"), primary_key=True)

    # Timestamp for auditing when the role was assigned
    assigned_at = Column(DateTime, default=datetime.utcnow)


# ✅ Role model – defines user roles like admin, editor, viewer, etc.
class Role(Base):
    __tablename__ = "roles"

    id = Column(Integer, primary_key=True, index=True)  # Unique role ID
    name = Column(String, unique=True, nullable=False)  # Role name (must be unique)

    # Reverse relationship: list of users who have this role
    users = relationship("User", secondary="user_roles", back_populates="roles")


# ✅ User model – stores application users
class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True)  # Unique user ID
    email = Column(String, unique=True, index=True)  # Email must be unique
    name = Column(String)  # User's full name
    password_hash = Column(String)  # Hashed password
    organization_id = Column(Integer)  # Optional org link (multi-tenant support)
    is_account_holder = Column(Boolean, default=False)  # Flag for org-level ownership

    # Timestamps for creation and last update
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow)

    # Many-to-many relationship with roles through the user_roles association table
    roles = relationship("Role", secondary="user_roles", back_populates="users")

    # One-to-many relationship with agents (this user created many agents)
    agents = relationship("Agent", back_populates="created_by_user")
