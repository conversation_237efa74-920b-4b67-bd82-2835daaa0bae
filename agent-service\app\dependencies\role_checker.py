from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>earer
from jose import J<PERSON><PERSON>rror, jwt
from typing import List
from app.config import SECRET_KEY, ALGORITHM

# ✅ OAuth2 scheme dependency for retrieving JWT tokens from the request
# The tokenUrl must match your login endpoint (with full prefix if using routers)
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/auth/login")

# ✅ Extract and return roles from the decoded JWT token
def get_current_user_roles(token: str = Depends(oauth2_scheme)) -> List[str]:
    try:
        # Decode JWT using secret key and algorithm
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])

        # Extract 'roles' from the token payload (must be a list of strings)
        roles = payload.get("roles")
        if not roles or not isinstance(roles, list):
            # Raise error if roles are missing or improperly formatted
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Roles not found or invalid"
            )
        return roles
    except J<PERSON><PERSON><PERSON>r as e:
        # Raise error if token is invalid or expired
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid token"
        )

# ✅ Dependency factory to enforce access control for specific roles
def require_role(required_roles: List[str]):
    # Inner function will be used as a dependency in route handlers
    def role_checker(roles: List[str] = Depends(get_current_user_roles)):
        # Check if any of the user's roles match the required roles
        if not any(role in required_roles for role in roles):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access forbidden. Required roles: {required_roles}"
            )
        return True  # Access granted
    return role_checker
