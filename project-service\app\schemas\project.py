from pydantic import BaseModel
from datetime import datetime
from pydantic import BaseModel, ConfigDict
from typing import Optional
from app.schemas.organization import OrganizationOut
from app.schemas.user import UserOut

class ProjectBase(BaseModel):
    name: str
    description: str | None = None
    organization_id: int
    user_id: int

class ProjectCreate(ProjectBase):
    pass

class ProjectUpdate(BaseModel):
    name: str | None = None
    description: str | None = None

class ProjectOut(BaseModel):
    id: int
    name: str
    description: Optional[str]
    organization: OrganizationOut
    user: UserOut

    model_config = ConfigDict(from_attributes=True)