from sqlalchemy import Column, <PERSON>teger, String, ForeignKey, DateTime
from sqlalchemy.sql import func
from app.database.connection import Base

from sqlalchemy.orm import relationship
from app.models.user import User

class Workflow(Base):
    __tablename__ = "workflows"

    id = Column(Integer, primary_key=True, index=True)
    organization_id = Column(Integer, ForeignKey("organizations.id"), nullable=False)
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False)

    creator = relationship("User", foreign_keys=[created_by])  # ✅ this is critical

    name = Column(String, nullable=False)
    status = Column(String, default="pending")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())



